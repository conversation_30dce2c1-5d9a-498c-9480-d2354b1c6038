{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["@typescript-eslint", "react", "react-hooks", "prettier", "unused-imports"], "rules": {"@typescript-eslint/no-explicit-any": "off", "react/react-in-jsx-scope": "off", "react/prop-types": "off", "@typescript-eslint/no-unused-vars": "off"}, "settings": {"react": {"version": "detect"}}}