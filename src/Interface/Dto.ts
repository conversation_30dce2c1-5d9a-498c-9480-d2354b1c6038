export interface INewsLetterInputDto {
  email: string,
}

export interface ISearchDropDownInputDto {
  id: number,
  label: string,
  value: string
}

export interface ICompetitionOptions {
  value: boolean,
  label: string,
}

interface IDeparturePort {
  id: number,
  label: string,
  value: string,
}


export const PriceRange = [
  { id: 0, label: "£0-500", value: "0-500", min: 0, max: 500 },
  { id: 1, label: "£500-1000", value: "500-1000", min: 500, max: 1000 },
  { id: 2, label: "£1000-1500", value: "1000-1500", min: 1000, max: 1500 },
  { id: 3, label: "£1500-2000", value: "1500-2000", min: 1500, max: 2000 },
  { id: 4, label: "£2000-3000", value: "2000-3000", min: 2000, max: 3000 },
  { id: 5, label: "£3000+", value: "3000+", min: 3000, max: 100000000000 },
];
export const SearchLandingData: IDeparturePort[] = [
  { id: 1, label: "Partner Offers", value: "Partner offers" },
  { id: 2, label: "Articles", value: "Articles" }
]

export interface successModalDto {
  type?: string,
  title?: string,
  text?: string
}




const checkList =   [
  {
    "id": 18,
    "name": "Get exclusive ideas and recommendations from global experts and cruise lovers",
    "url": null
  },
  {
    "id": 19,
    "name": "Save 10-15% on offers from the world's best cruise lines",
    "url": null
  },
  {
    "id": 20,
    "name": "get discounts on top of cruise line discounts",
    "url": null
  },
  {
    "id": 21,
    "name": "Completely free to join",
    "url": null
  }
]

export const ctaInfoBlocks = [
  {
    "id": 14,
    "__component": "blocks.call-to-actions",
    "heading": "Huge savings when booking with Cruise Collective",
    "description": "Unlock exclusive discounts and special offers tailored to your passions. Book with Cruise Collective for unbeatable savings on top cruise lines, combining your love for travel with unique hobbies and experiences. ",
    "sort": 3,
    "cruise_lines": {
      "data": [
        {
          "id": 18,
          "attributes": {
            "title": "AmaWaterways",
            "createdAt": "2024-01-09T09:11:27.762Z",
            "updatedAt": "2024-09-01T09:54:20.657Z",
            "excerpt": "Founded in 2002 by industry veterans Rudi Schreiner, Kristin Karst, and Jimmy Murphy, AmaWaterways has redefined the river cruise experience with a commitment to innovation and excellence. \n\n",
            "slug": "ama-waterways",
            "video": null,
            "offer": "£150",
            "affiliate_link": "https://www.amawaterways.co.uk/",
            "publishedAt": "2024-01-09T09:11:27.762Z",
            "coupon": null,
            "amount": null,
            "headline": null,
            "featured_image": {
              "data": [
                {
                  "id": 217,
                  "attributes": {
                    "name": "CELLO_exterior.jpg",
                    "alternativeText": "Cello exterior",
                    "caption": null,
                    "width": 2000,
                    "height": 1335,
                    "formats": {
                      "thumbnail": {
                        "name": "thumbnail_CELLO_exterior.jpg",
                        "hash": "thumbnail_CELLO_exterior_4c3128025a",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 234,
                        "height": 156,
                        "size": 6.27,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_CELLO_exterior_4c3128025a.jpg"
                      },
                      "small": {
                        "name": "small_CELLO_exterior.jpg",
                        "hash": "small_CELLO_exterior_4c3128025a",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 500,
                        "height": 334,
                        "size": 23.55,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_CELLO_exterior_4c3128025a.jpg"
                      },
                      "medium": {
                        "name": "medium_CELLO_exterior.jpg",
                        "hash": "medium_CELLO_exterior_4c3128025a",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 750,
                        "height": 501,
                        "size": 50.76,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_CELLO_exterior_4c3128025a.jpg"
                      },
                      "large": {
                        "name": "large_CELLO_exterior.jpg",
                        "hash": "large_CELLO_exterior_4c3128025a",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 1000,
                        "height": 668,
                        "size": 86.75,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_CELLO_exterior_4c3128025a.jpg"
                      }
                    },
                    "hash": "CELLO_exterior_4c3128025a",
                    "ext": ".jpg",
                    "mime": "image/jpeg",
                    "size": 321.63,
                    "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/CELLO_exterior_4c3128025a.jpg",
                    "previewUrl": null,
                    "provider": "aws-s3",
                    "provider_metadata": null,
                    "createdAt": "2023-12-06T23:24:37.634Z",
                    "updatedAt": "2023-12-06T23:24:37.634Z"
                  }
                },
                {
                  "id": 220,
                  "attributes": {
                    "name": "PRIMA_exterior_budapest.jpg",
                    "alternativeText": "Prima exterior",
                    "caption": null,
                    "width": 2000,
                    "height": 1333,
                    "formats": {
                      "thumbnail": {
                        "name": "thumbnail_PRIMA_exterior_budapest.jpg",
                        "hash": "thumbnail_PRIMA_exterior_budapest_22c316b453",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 234,
                        "height": 156,
                        "size": 8.65,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_PRIMA_exterior_budapest_22c316b453.jpg"
                      },
                      "small": {
                        "name": "small_PRIMA_exterior_budapest.jpg",
                        "hash": "small_PRIMA_exterior_budapest_22c316b453",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 500,
                        "height": 333,
                        "size": 35.18,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_PRIMA_exterior_budapest_22c316b453.jpg"
                      },
                      "medium": {
                        "name": "medium_PRIMA_exterior_budapest.jpg",
                        "hash": "medium_PRIMA_exterior_budapest_22c316b453",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 750,
                        "height": 500,
                        "size": 80.18,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_PRIMA_exterior_budapest_22c316b453.jpg"
                      },
                      "large": {
                        "name": "large_PRIMA_exterior_budapest.jpg",
                        "hash": "large_PRIMA_exterior_budapest_22c316b453",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 1000,
                        "height": 667,
                        "size": 142.5,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_PRIMA_exterior_budapest_22c316b453.jpg"
                      }
                    },
                    "hash": "PRIMA_exterior_budapest_22c316b453",
                    "ext": ".jpg",
                    "mime": "image/jpeg",
                    "size": 566.05,
                    "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/PRIMA_exterior_budapest_22c316b453.jpg",
                    "previewUrl": null,
                    "provider": "aws-s3",
                    "provider_metadata": null,
                    "createdAt": "2023-12-06T23:24:38.995Z",
                    "updatedAt": "2023-12-06T23:24:38.995Z"
                  }
                },
                {
                  "id": 218,
                  "attributes": {
                    "name": "DANTE_exterior.jpg",
                    "alternativeText": "Dante exterior",
                    "caption": null,
                    "width": 2000,
                    "height": 1333,
                    "formats": {
                      "thumbnail": {
                        "name": "thumbnail_DANTE_exterior.jpg",
                        "hash": "thumbnail_DANTE_exterior_95bac43025",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 234,
                        "height": 156,
                        "size": 8.49,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_DANTE_exterior_95bac43025.jpg"
                      },
                      "small": {
                        "name": "small_DANTE_exterior.jpg",
                        "hash": "small_DANTE_exterior_95bac43025",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 500,
                        "height": 333,
                        "size": 33.86,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_DANTE_exterior_95bac43025.jpg"
                      },
                      "medium": {
                        "name": "medium_DANTE_exterior.jpg",
                        "hash": "medium_DANTE_exterior_95bac43025",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 750,
                        "height": 500,
                        "size": 74.53,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_DANTE_exterior_95bac43025.jpg"
                      },
                      "large": {
                        "name": "large_DANTE_exterior.jpg",
                        "hash": "large_DANTE_exterior_95bac43025",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 1000,
                        "height": 667,
                        "size": 127.49,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_DANTE_exterior_95bac43025.jpg"
                      }
                    },
                    "hash": "DANTE_exterior_95bac43025",
                    "ext": ".jpg",
                    "mime": "image/jpeg",
                    "size": 444.37,
                    "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/DANTE_exterior_95bac43025.jpg",
                    "previewUrl": null,
                    "provider": "aws-s3",
                    "provider_metadata": null,
                    "createdAt": "2023-12-06T23:24:38.415Z",
                    "updatedAt": "2023-12-06T23:24:38.415Z"
                  }
                },
                {
                  "id": 216,
                  "attributes": {
                    "name": "DARA_exterior.jpg",
                    "alternativeText": "Dara exterior",
                    "caption": null,
                    "width": 2000,
                    "height": 1231,
                    "formats": {
                      "thumbnail": {
                        "name": "thumbnail_DARA_exterior.jpg",
                        "hash": "thumbnail_DARA_exterior_569832fda6",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 245,
                        "height": 150,
                        "size": 6.6,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_DARA_exterior_569832fda6.jpg"
                      },
                      "medium": {
                        "name": "medium_DARA_exterior.jpg",
                        "hash": "medium_DARA_exterior_569832fda6",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 750,
                        "height": 462,
                        "size": 47.63,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_DARA_exterior_569832fda6.jpg"
                      },
                      "small": {
                        "name": "small_DARA_exterior.jpg",
                        "hash": "small_DARA_exterior_569832fda6",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 500,
                        "height": 308,
                        "size": 22.46,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_DARA_exterior_569832fda6.jpg"
                      },
                      "large": {
                        "name": "large_DARA_exterior.jpg",
                        "hash": "large_DARA_exterior_569832fda6",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 1000,
                        "height": 616,
                        "size": 79.62,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_DARA_exterior_569832fda6.jpg"
                      }
                    },
                    "hash": "DARA_exterior_569832fda6",
                    "ext": ".jpg",
                    "mime": "image/jpeg",
                    "size": 277.12,
                    "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/DARA_exterior_569832fda6.jpg",
                    "previewUrl": null,
                    "provider": "aws-s3",
                    "provider_metadata": null,
                    "createdAt": "2023-12-06T23:24:35.413Z",
                    "updatedAt": "2023-12-06T23:24:35.413Z"
                  }
                }
              ]
            },
            "logo": {
              "data": {
                "id": 166,
                "attributes": {
                  "name": "ama-waterways-logo.png",
                  "alternativeText": "AMAWaterways logo",
                  "caption": null,
                  "width": 3174,
                  "height": 303,
                  "formats": {
                    "thumbnail": {
                      "name": "thumbnail_ama-waterways-logo.png",
                      "hash": "thumbnail_ama_waterways_logo_8e8606101b",
                      "ext": ".png",
                      "mime": "image/png",
                      "path": null,
                      "width": 245,
                      "height": 23,
                      "size": 6.99,
                      "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_ama_waterways_logo_8e8606101b.png"
                    },
                    "large": {
                      "name": "large_ama-waterways-logo.png",
                      "hash": "large_ama_waterways_logo_8e8606101b",
                      "ext": ".png",
                      "mime": "image/png",
                      "path": null,
                      "width": 1000,
                      "height": 95,
                      "size": 42.03,
                      "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_ama_waterways_logo_8e8606101b.png"
                    },
                    "medium": {
                      "name": "medium_ama-waterways-logo.png",
                      "hash": "medium_ama_waterways_logo_8e8606101b",
                      "ext": ".png",
                      "mime": "image/png",
                      "path": null,
                      "width": 750,
                      "height": 72,
                      "size": 29.85,
                      "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_ama_waterways_logo_8e8606101b.png"
                    },
                    "small": {
                      "name": "small_ama-waterways-logo.png",
                      "hash": "small_ama_waterways_logo_8e8606101b",
                      "ext": ".png",
                      "mime": "image/png",
                      "path": null,
                      "width": 500,
                      "height": 48,
                      "size": 17.63,
                      "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_ama_waterways_logo_8e8606101b.png"
                    }
                  },
                  "hash": "ama_waterways_logo_8e8606101b",
                  "ext": ".png",
                  "mime": "image/png",
                  "size": 32.99,
                  "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/ama_waterways_logo_8e8606101b.png",
                  "previewUrl": null,
                  "provider": "aws-s3",
                  "provider_metadata": null,
                  "createdAt": "2023-11-20T23:46:25.339Z",
                  "updatedAt": "2023-11-20T23:46:25.339Z"
                }
              }
            },
            "seo": {
              "id": 10,
              "metaTitle": "AmaWaterways",
              "metaDescription": "AmaWaterways has redefined the river cruise experience with a commitment to innovation and excellence. ",
              "keywords": null,
              "metaRobots": null,
              "structuredData": null,
              "metaViewport": null,
              "canonicalURL": null,
              "metaImage": {
                "data": null
              },
              "metaSocial": []
            },
            "calloutbox": {
              "id": 1,
              "title": "Save 10% with Cruise Collective",
              "url": "https://www.amawaterways.co.uk/",
              "saving": "20%",
              "description": "DISCOUNT2024",
              "sub_heading": null,
              "button_text": null,
              "button_url": null,
              "how_to_use": []
            },
            "blocks": [
              {
                "id": 1,
                "__component": "blocks.promo",
                "title": "Test",
                "description": "Testing? ",
                "sort": null
              },
              {
                "id": 1,
                "__component": "blocks.info-box-slider",
                "url": null,
                "url_text": null,
                "tab_title": "Test",
                "description": "<p>Question</p>",
                "sort": 0,
                "title": null,
                "image": {
                  "data": null
                }
              }
            ]
          }
        },
        {
          "id": 6,
          "attributes": {
            "title": "Hurtigruten",
            "createdAt": "2023-10-22T09:55:06.082Z",
            "updatedAt": "2024-03-19T15:26:05.461Z",
            "excerpt": "A journey of unparalleled beauty and cultural immersion with Hurtigruten. A voyage that highlights Norway's breathtaking landscapes and rich heritage in a way that is truly authentic and utterly unforgettable.\n<br>\n<br>\nCruise Collective members save 5% when booking with Hurtigruten.",
            "slug": "hurtigruten",
            "video": null,
            "offer": "5%",
            "affiliate_link": "https://www.hurtigruten.com/en-gb?utm_source=website&utm_id=cruisecollective",
            "publishedAt": "2023-10-22T09:55:06.082Z",
            "coupon": null,
            "amount": null,
            "headline": null,
            "featured_image": {
              "data": [
                {
                  "id": 210,
                  "attributes": {
                    "name": "Hurtigruten Coastal",
                    "alternativeText": "Hurtigruten Coastal",
                    "caption": null,
                    "width": 2122,
                    "height": 1412,
                    "formats": {
                      "thumbnail": {
                        "name": "thumbnail_Hurtigruten Coastal",
                        "hash": "thumbnail_Hurtigruten_Coastal_2dde4ad529",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 235,
                        "height": 156,
                        "size": 8.03,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_Hurtigruten_Coastal_2dde4ad529.jpg"
                      },
                      "small": {
                        "name": "small_Hurtigruten Coastal",
                        "hash": "small_Hurtigruten_Coastal_2dde4ad529",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 500,
                        "height": 333,
                        "size": 28.84,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_Hurtigruten_Coastal_2dde4ad529.jpg"
                      },
                      "large": {
                        "name": "large_Hurtigruten Coastal",
                        "hash": "large_Hurtigruten_Coastal_2dde4ad529",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 1000,
                        "height": 665,
                        "size": 91.26,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_Hurtigruten_Coastal_2dde4ad529.jpg"
                      },
                      "medium": {
                        "name": "medium_Hurtigruten Coastal",
                        "hash": "medium_Hurtigruten_Coastal_2dde4ad529",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 750,
                        "height": 499,
                        "size": 56.47,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_Hurtigruten_Coastal_2dde4ad529.jpg"
                      }
                    },
                    "hash": "Hurtigruten_Coastal_2dde4ad529",
                    "ext": ".jpg",
                    "mime": "image/jpeg",
                    "size": 319.56,
                    "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/Hurtigruten_Coastal_2dde4ad529.jpg",
                    "previewUrl": null,
                    "provider": "aws-s3",
                    "provider_metadata": null,
                    "createdAt": "2023-12-06T23:06:40.682Z",
                    "updatedAt": "2023-12-06T23:06:40.682Z"
                  }
                },
                {
                  "id": 211,
                  "attributes": {
                    "name": "Raftsundet Norway Hurtigruten",
                    "alternativeText": "Raftsundet Norway Hurtigruten",
                    "caption": null,
                    "width": 7216,
                    "height": 5412,
                    "formats": {
                      "thumbnail": {
                        "name": "thumbnail_Raftsundet Norway Hurtigruten",
                        "hash": "thumbnail_Raftsundet_Norway_Hurtigruten_6de3bb6760",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 208,
                        "height": 156,
                        "size": 8.94,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_Raftsundet_Norway_Hurtigruten_6de3bb6760.jpg"
                      },
                      "medium": {
                        "name": "medium_Raftsundet Norway Hurtigruten",
                        "hash": "medium_Raftsundet_Norway_Hurtigruten_6de3bb6760",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 750,
                        "height": 562,
                        "size": 86.02,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_Raftsundet_Norway_Hurtigruten_6de3bb6760.jpg"
                      },
                      "small": {
                        "name": "small_Raftsundet Norway Hurtigruten",
                        "hash": "small_Raftsundet_Norway_Hurtigruten_6de3bb6760",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 500,
                        "height": 375,
                        "size": 41.55,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_Raftsundet_Norway_Hurtigruten_6de3bb6760.jpg"
                      },
                      "large": {
                        "name": "large_Raftsundet Norway Hurtigruten",
                        "hash": "large_Raftsundet_Norway_Hurtigruten_6de3bb6760",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 1000,
                        "height": 750,
                        "size": 150.04,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_Raftsundet_Norway_Hurtigruten_6de3bb6760.jpg"
                      }
                    },
                    "hash": "Raftsundet_Norway_Hurtigruten_6de3bb6760",
                    "ext": ".jpg",
                    "mime": "image/jpeg",
                    "size": 5273.59,
                    "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/Raftsundet_Norway_Hurtigruten_6de3bb6760.jpg",
                    "previewUrl": null,
                    "provider": "aws-s3",
                    "provider_metadata": null,
                    "createdAt": "2023-12-06T23:07:39.731Z",
                    "updatedAt": "2023-12-06T23:07:39.731Z"
                  }
                },
                {
                  "id": 212,
                  "attributes": {
                    "name": "Norway Northern Lights Hurtigruten",
                    "alternativeText": "Norway Northern Lights Hurtigruten",
                    "caption": null,
                    "width": 7298,
                    "height": 4871,
                    "formats": {
                      "thumbnail": {
                        "name": "thumbnail_Norway Northern Lights Hurtigruten",
                        "hash": "thumbnail_Norway_Northern_Lights_Hurtigruten_05bc2221be",
                        "ext": ".JPG",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 234,
                        "height": 156,
                        "size": 5.42,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_Norway_Northern_Lights_Hurtigruten_05bc2221be.JPG"
                      },
                      "medium": {
                        "name": "medium_Norway Northern Lights Hurtigruten",
                        "hash": "medium_Norway_Northern_Lights_Hurtigruten_05bc2221be",
                        "ext": ".JPG",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 750,
                        "height": 500,
                        "size": 38.86,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_Norway_Northern_Lights_Hurtigruten_05bc2221be.JPG"
                      },
                      "small": {
                        "name": "small_Norway Northern Lights Hurtigruten",
                        "hash": "small_Norway_Northern_Lights_Hurtigruten_05bc2221be",
                        "ext": ".JPG",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 500,
                        "height": 333,
                        "size": 19.24,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_Norway_Northern_Lights_Hurtigruten_05bc2221be.JPG"
                      },
                      "large": {
                        "name": "large_Norway Northern Lights Hurtigruten",
                        "hash": "large_Norway_Northern_Lights_Hurtigruten_05bc2221be",
                        "ext": ".JPG",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 1000,
                        "height": 667,
                        "size": 66.93,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_Norway_Northern_Lights_Hurtigruten_05bc2221be.JPG"
                      }
                    },
                    "hash": "Norway_Northern_Lights_Hurtigruten_05bc2221be",
                    "ext": ".JPG",
                    "mime": "image/jpeg",
                    "size": 6163.63,
                    "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/Norway_Northern_Lights_Hurtigruten_05bc2221be.JPG",
                    "previewUrl": null,
                    "provider": "aws-s3",
                    "provider_metadata": null,
                    "createdAt": "2023-12-06T23:07:46.765Z",
                    "updatedAt": "2024-03-13T16:15:33.450Z"
                  }
                },
                {
                  "id": 137,
                  "attributes": {
                    "name": "hurtigen river cruise.jpg",
                    "alternativeText": null,
                    "caption": null,
                    "width": 2000,
                    "height": 1333,
                    "formats": {
                      "thumbnail": {
                        "name": "thumbnail_hurtigen river cruise.jpg",
                        "hash": "thumbnail_hurtigen_river_cruise_036330481e",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 234,
                        "height": 156,
                        "size": 6.38,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_hurtigen_river_cruise_036330481e.jpg"
                      }
                    },
                    "hash": "hurtigen_river_cruise_036330481e",
                    "ext": ".jpg",
                    "mime": "image/jpeg",
                    "size": 284.93,
                    "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/hurtigen_river_cruise_036330481e.jpg",
                    "previewUrl": null,
                    "provider": "aws-s3",
                    "provider_metadata": null,
                    "createdAt": "2023-11-05T10:49:19.954Z",
                    "updatedAt": "2023-11-05T10:49:19.954Z"
                  }
                }
              ]
            },
            "logo": {
              "data": {
                "id": 311,
                "attributes": {
                  "name": "Hurtigruten Coastal logo",
                  "alternativeText": "Hurtigruten Coastal logo",
                  "caption": null,
                  "width": 5680,
                  "height": 2024,
                  "formats": {
                    "thumbnail": {
                      "name": "thumbnail_HR_logo_main_POS_RGB.png",
                      "hash": "thumbnail_HR_logo_main_POS_RGB_f5b6e7c9f7",
                      "ext": ".png",
                      "mime": "image/png",
                      "path": null,
                      "width": 245,
                      "height": 87,
                      "size": 3.67,
                      "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_HR_logo_main_POS_RGB_f5b6e7c9f7.png"
                    },
                    "large": {
                      "name": "large_HR_logo_main_POS_RGB.png",
                      "hash": "large_HR_logo_main_POS_RGB_f5b6e7c9f7",
                      "ext": ".png",
                      "mime": "image/png",
                      "path": null,
                      "width": 1000,
                      "height": 356,
                      "size": 16.78,
                      "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_HR_logo_main_POS_RGB_f5b6e7c9f7.png"
                    },
                    "small": {
                      "name": "small_HR_logo_main_POS_RGB.png",
                      "hash": "small_HR_logo_main_POS_RGB_f5b6e7c9f7",
                      "ext": ".png",
                      "mime": "image/png",
                      "path": null,
                      "width": 500,
                      "height": 178,
                      "size": 7.93,
                      "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_HR_logo_main_POS_RGB_f5b6e7c9f7.png"
                    },
                    "medium": {
                      "name": "medium_HR_logo_main_POS_RGB.png",
                      "hash": "medium_HR_logo_main_POS_RGB_f5b6e7c9f7",
                      "ext": ".png",
                      "mime": "image/png",
                      "path": null,
                      "width": 750,
                      "height": 267,
                      "size": 12.21,
                      "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_HR_logo_main_POS_RGB_f5b6e7c9f7.png"
                    }
                  },
                  "hash": "HR_logo_main_POS_RGB_f5b6e7c9f7",
                  "ext": ".png",
                  "mime": "image/png",
                  "size": 38.29,
                  "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/HR_logo_main_POS_RGB_f5b6e7c9f7.png",
                  "previewUrl": null,
                  "provider": "aws-s3",
                  "provider_metadata": null,
                  "createdAt": "2024-02-28T16:33:41.840Z",
                  "updatedAt": "2024-02-28T16:34:04.915Z"
                }
              }
            },
            "seo": null,
            "calloutbox": null,
            "blocks": []
          }
        }
      ]
    },
    "checklist": checkList
  }
]

export const ctaInfoDiscount = [
  {
    "id": 14,
    "__component": "blocks.call-to-actions",
    "heading": "Huge savings when booking with Cruise Collective",
    "description": "Become part of the Collective and discover the world of cruising through the experiences of those that love it. Helping you to do more of what you love at sea – Exclusive articles, discounts and ideas.",
    "sort": 3,
    "cruise_lines": {
      "data": [
        {
          "id": 18,
          "attributes": {
            "title": "AmaWaterways",
            "createdAt": "2024-01-09T09:11:27.762Z",
            "updatedAt": "2024-09-01T09:54:20.657Z",
            "excerpt": "Founded in 2002 by industry veterans Rudi Schreiner, Kristin Karst, and Jimmy Murphy, AmaWaterways has redefined the river cruise experience with a commitment to innovation and excellence. \n\n",
            "slug": "ama-waterways",
            "video": null,
            "offer": "£150",
            "affiliate_link": "https://www.amawaterways.co.uk/",
            "publishedAt": "2024-01-09T09:11:27.762Z",
            "coupon": null,
            "amount": null,
            "headline": null,
            "featured_image": {
              "data": [
                {
                  "id": 217,
                  "attributes": {
                    "name": "CELLO_exterior.jpg",
                    "alternativeText": "Cello exterior",
                    "caption": null,
                    "width": 2000,
                    "height": 1335,
                    "formats": {
                      "thumbnail": {
                        "name": "thumbnail_CELLO_exterior.jpg",
                        "hash": "thumbnail_CELLO_exterior_4c3128025a",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 234,
                        "height": 156,
                        "size": 6.27,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_CELLO_exterior_4c3128025a.jpg"
                      },
                      "small": {
                        "name": "small_CELLO_exterior.jpg",
                        "hash": "small_CELLO_exterior_4c3128025a",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 500,
                        "height": 334,
                        "size": 23.55,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_CELLO_exterior_4c3128025a.jpg"
                      },
                      "medium": {
                        "name": "medium_CELLO_exterior.jpg",
                        "hash": "medium_CELLO_exterior_4c3128025a",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 750,
                        "height": 501,
                        "size": 50.76,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_CELLO_exterior_4c3128025a.jpg"
                      },
                      "large": {
                        "name": "large_CELLO_exterior.jpg",
                        "hash": "large_CELLO_exterior_4c3128025a",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 1000,
                        "height": 668,
                        "size": 86.75,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_CELLO_exterior_4c3128025a.jpg"
                      }
                    },
                    "hash": "CELLO_exterior_4c3128025a",
                    "ext": ".jpg",
                    "mime": "image/jpeg",
                    "size": 321.63,
                    "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/CELLO_exterior_4c3128025a.jpg",
                    "previewUrl": null,
                    "provider": "aws-s3",
                    "provider_metadata": null,
                    "createdAt": "2023-12-06T23:24:37.634Z",
                    "updatedAt": "2023-12-06T23:24:37.634Z"
                  }
                },
                {
                  "id": 220,
                  "attributes": {
                    "name": "PRIMA_exterior_budapest.jpg",
                    "alternativeText": "Prima exterior",
                    "caption": null,
                    "width": 2000,
                    "height": 1333,
                    "formats": {
                      "thumbnail": {
                        "name": "thumbnail_PRIMA_exterior_budapest.jpg",
                        "hash": "thumbnail_PRIMA_exterior_budapest_22c316b453",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 234,
                        "height": 156,
                        "size": 8.65,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_PRIMA_exterior_budapest_22c316b453.jpg"
                      },
                      "small": {
                        "name": "small_PRIMA_exterior_budapest.jpg",
                        "hash": "small_PRIMA_exterior_budapest_22c316b453",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 500,
                        "height": 333,
                        "size": 35.18,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_PRIMA_exterior_budapest_22c316b453.jpg"
                      },
                      "medium": {
                        "name": "medium_PRIMA_exterior_budapest.jpg",
                        "hash": "medium_PRIMA_exterior_budapest_22c316b453",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 750,
                        "height": 500,
                        "size": 80.18,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_PRIMA_exterior_budapest_22c316b453.jpg"
                      },
                      "large": {
                        "name": "large_PRIMA_exterior_budapest.jpg",
                        "hash": "large_PRIMA_exterior_budapest_22c316b453",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 1000,
                        "height": 667,
                        "size": 142.5,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_PRIMA_exterior_budapest_22c316b453.jpg"
                      }
                    },
                    "hash": "PRIMA_exterior_budapest_22c316b453",
                    "ext": ".jpg",
                    "mime": "image/jpeg",
                    "size": 566.05,
                    "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/PRIMA_exterior_budapest_22c316b453.jpg",
                    "previewUrl": null,
                    "provider": "aws-s3",
                    "provider_metadata": null,
                    "createdAt": "2023-12-06T23:24:38.995Z",
                    "updatedAt": "2023-12-06T23:24:38.995Z"
                  }
                },
                {
                  "id": 218,
                  "attributes": {
                    "name": "DANTE_exterior.jpg",
                    "alternativeText": "Dante exterior",
                    "caption": null,
                    "width": 2000,
                    "height": 1333,
                    "formats": {
                      "thumbnail": {
                        "name": "thumbnail_DANTE_exterior.jpg",
                        "hash": "thumbnail_DANTE_exterior_95bac43025",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 234,
                        "height": 156,
                        "size": 8.49,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_DANTE_exterior_95bac43025.jpg"
                      },
                      "small": {
                        "name": "small_DANTE_exterior.jpg",
                        "hash": "small_DANTE_exterior_95bac43025",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 500,
                        "height": 333,
                        "size": 33.86,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_DANTE_exterior_95bac43025.jpg"
                      },
                      "medium": {
                        "name": "medium_DANTE_exterior.jpg",
                        "hash": "medium_DANTE_exterior_95bac43025",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 750,
                        "height": 500,
                        "size": 74.53,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_DANTE_exterior_95bac43025.jpg"
                      },
                      "large": {
                        "name": "large_DANTE_exterior.jpg",
                        "hash": "large_DANTE_exterior_95bac43025",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 1000,
                        "height": 667,
                        "size": 127.49,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_DANTE_exterior_95bac43025.jpg"
                      }
                    },
                    "hash": "DANTE_exterior_95bac43025",
                    "ext": ".jpg",
                    "mime": "image/jpeg",
                    "size": 444.37,
                    "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/DANTE_exterior_95bac43025.jpg",
                    "previewUrl": null,
                    "provider": "aws-s3",
                    "provider_metadata": null,
                    "createdAt": "2023-12-06T23:24:38.415Z",
                    "updatedAt": "2023-12-06T23:24:38.415Z"
                  }
                },
                {
                  "id": 216,
                  "attributes": {
                    "name": "DARA_exterior.jpg",
                    "alternativeText": "Dara exterior",
                    "caption": null,
                    "width": 2000,
                    "height": 1231,
                    "formats": {
                      "thumbnail": {
                        "name": "thumbnail_DARA_exterior.jpg",
                        "hash": "thumbnail_DARA_exterior_569832fda6",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 245,
                        "height": 150,
                        "size": 6.6,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_DARA_exterior_569832fda6.jpg"
                      },
                      "medium": {
                        "name": "medium_DARA_exterior.jpg",
                        "hash": "medium_DARA_exterior_569832fda6",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 750,
                        "height": 462,
                        "size": 47.63,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_DARA_exterior_569832fda6.jpg"
                      },
                      "small": {
                        "name": "small_DARA_exterior.jpg",
                        "hash": "small_DARA_exterior_569832fda6",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 500,
                        "height": 308,
                        "size": 22.46,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_DARA_exterior_569832fda6.jpg"
                      },
                      "large": {
                        "name": "large_DARA_exterior.jpg",
                        "hash": "large_DARA_exterior_569832fda6",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 1000,
                        "height": 616,
                        "size": 79.62,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_DARA_exterior_569832fda6.jpg"
                      }
                    },
                    "hash": "DARA_exterior_569832fda6",
                    "ext": ".jpg",
                    "mime": "image/jpeg",
                    "size": 277.12,
                    "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/DARA_exterior_569832fda6.jpg",
                    "previewUrl": null,
                    "provider": "aws-s3",
                    "provider_metadata": null,
                    "createdAt": "2023-12-06T23:24:35.413Z",
                    "updatedAt": "2023-12-06T23:24:35.413Z"
                  }
                }
              ]
            },
            "logo": {
              "data": {
                "id": 166,
                "attributes": {
                  "name": "ama-waterways-logo.png",
                  "alternativeText": "AMAWaterways logo",
                  "caption": null,
                  "width": 3174,
                  "height": 303,
                  "formats": {
                    "thumbnail": {
                      "name": "thumbnail_ama-waterways-logo.png",
                      "hash": "thumbnail_ama_waterways_logo_8e8606101b",
                      "ext": ".png",
                      "mime": "image/png",
                      "path": null,
                      "width": 245,
                      "height": 23,
                      "size": 6.99,
                      "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_ama_waterways_logo_8e8606101b.png"
                    },
                    "large": {
                      "name": "large_ama-waterways-logo.png",
                      "hash": "large_ama_waterways_logo_8e8606101b",
                      "ext": ".png",
                      "mime": "image/png",
                      "path": null,
                      "width": 1000,
                      "height": 95,
                      "size": 42.03,
                      "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_ama_waterways_logo_8e8606101b.png"
                    },
                    "medium": {
                      "name": "medium_ama-waterways-logo.png",
                      "hash": "medium_ama_waterways_logo_8e8606101b",
                      "ext": ".png",
                      "mime": "image/png",
                      "path": null,
                      "width": 750,
                      "height": 72,
                      "size": 29.85,
                      "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_ama_waterways_logo_8e8606101b.png"
                    },
                    "small": {
                      "name": "small_ama-waterways-logo.png",
                      "hash": "small_ama_waterways_logo_8e8606101b",
                      "ext": ".png",
                      "mime": "image/png",
                      "path": null,
                      "width": 500,
                      "height": 48,
                      "size": 17.63,
                      "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_ama_waterways_logo_8e8606101b.png"
                    }
                  },
                  "hash": "ama_waterways_logo_8e8606101b",
                  "ext": ".png",
                  "mime": "image/png",
                  "size": 32.99,
                  "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/ama_waterways_logo_8e8606101b.png",
                  "previewUrl": null,
                  "provider": "aws-s3",
                  "provider_metadata": null,
                  "createdAt": "2023-11-20T23:46:25.339Z",
                  "updatedAt": "2023-11-20T23:46:25.339Z"
                }
              }
            },
            "seo": {
              "id": 10,
              "metaTitle": "AmaWaterways",
              "metaDescription": "AmaWaterways has redefined the river cruise experience with a commitment to innovation and excellence. ",
              "keywords": null,
              "metaRobots": null,
              "structuredData": null,
              "metaViewport": null,
              "canonicalURL": null,
              "metaImage": {
                "data": null
              },
              "metaSocial": []
            },
            "calloutbox": {
              "id": 1,
              "title": "Save 10% with Cruise Collective",
              "url": "https://www.amawaterways.co.uk/",
              "saving": "20%",
              "description": "DISCOUNT2024",
              "sub_heading": null,
              "button_text": null,
              "button_url": null,
              "how_to_use": []
            },
            "blocks": [
              {
                "id": 1,
                "__component": "blocks.promo",
                "title": "Test",
                "description": "Testing? ",
                "sort": null
              },
              {
                "id": 1,
                "__component": "blocks.info-box-slider",
                "url": null,
                "url_text": null,
                "tab_title": "Test",
                "description": "<p>Question</p>",
                "sort": 0,
                "title": null,
                "image": {
                  "data": null
                }
              }
            ]
          }
        },
        {
          "id": 6,
          "attributes": {
            "title": "Hurtigruten",
            "createdAt": "2023-10-22T09:55:06.082Z",
            "updatedAt": "2024-03-19T15:26:05.461Z",
            "excerpt": "A journey of unparalleled beauty and cultural immersion with Hurtigruten. A voyage that highlights Norway's breathtaking landscapes and rich heritage in a way that is truly authentic and utterly unforgettable.\n<br>\n<br>\nCruise Collective members save 5% when booking with Hurtigruten.",
            "slug": "hurtigruten",
            "video": null,
            "offer": "5%",
            "affiliate_link": "https://www.hurtigruten.com/en-gb?utm_source=website&utm_id=cruisecollective",
            "publishedAt": "2023-10-22T09:55:06.082Z",
            "coupon": null,
            "amount": null,
            "headline": null,
            "featured_image": {
              "data": [
                {
                  "id": 210,
                  "attributes": {
                    "name": "Hurtigruten Coastal",
                    "alternativeText": "Hurtigruten Coastal",
                    "caption": null,
                    "width": 2122,
                    "height": 1412,
                    "formats": {
                      "thumbnail": {
                        "name": "thumbnail_Hurtigruten Coastal",
                        "hash": "thumbnail_Hurtigruten_Coastal_2dde4ad529",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 235,
                        "height": 156,
                        "size": 8.03,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_Hurtigruten_Coastal_2dde4ad529.jpg"
                      },
                      "small": {
                        "name": "small_Hurtigruten Coastal",
                        "hash": "small_Hurtigruten_Coastal_2dde4ad529",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 500,
                        "height": 333,
                        "size": 28.84,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_Hurtigruten_Coastal_2dde4ad529.jpg"
                      },
                      "large": {
                        "name": "large_Hurtigruten Coastal",
                        "hash": "large_Hurtigruten_Coastal_2dde4ad529",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 1000,
                        "height": 665,
                        "size": 91.26,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_Hurtigruten_Coastal_2dde4ad529.jpg"
                      },
                      "medium": {
                        "name": "medium_Hurtigruten Coastal",
                        "hash": "medium_Hurtigruten_Coastal_2dde4ad529",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 750,
                        "height": 499,
                        "size": 56.47,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_Hurtigruten_Coastal_2dde4ad529.jpg"
                      }
                    },
                    "hash": "Hurtigruten_Coastal_2dde4ad529",
                    "ext": ".jpg",
                    "mime": "image/jpeg",
                    "size": 319.56,
                    "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/Hurtigruten_Coastal_2dde4ad529.jpg",
                    "previewUrl": null,
                    "provider": "aws-s3",
                    "provider_metadata": null,
                    "createdAt": "2023-12-06T23:06:40.682Z",
                    "updatedAt": "2023-12-06T23:06:40.682Z"
                  }
                },
                {
                  "id": 211,
                  "attributes": {
                    "name": "Raftsundet Norway Hurtigruten",
                    "alternativeText": "Raftsundet Norway Hurtigruten",
                    "caption": null,
                    "width": 7216,
                    "height": 5412,
                    "formats": {
                      "thumbnail": {
                        "name": "thumbnail_Raftsundet Norway Hurtigruten",
                        "hash": "thumbnail_Raftsundet_Norway_Hurtigruten_6de3bb6760",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 208,
                        "height": 156,
                        "size": 8.94,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_Raftsundet_Norway_Hurtigruten_6de3bb6760.jpg"
                      },
                      "medium": {
                        "name": "medium_Raftsundet Norway Hurtigruten",
                        "hash": "medium_Raftsundet_Norway_Hurtigruten_6de3bb6760",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 750,
                        "height": 562,
                        "size": 86.02,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_Raftsundet_Norway_Hurtigruten_6de3bb6760.jpg"
                      },
                      "small": {
                        "name": "small_Raftsundet Norway Hurtigruten",
                        "hash": "small_Raftsundet_Norway_Hurtigruten_6de3bb6760",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 500,
                        "height": 375,
                        "size": 41.55,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_Raftsundet_Norway_Hurtigruten_6de3bb6760.jpg"
                      },
                      "large": {
                        "name": "large_Raftsundet Norway Hurtigruten",
                        "hash": "large_Raftsundet_Norway_Hurtigruten_6de3bb6760",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 1000,
                        "height": 750,
                        "size": 150.04,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_Raftsundet_Norway_Hurtigruten_6de3bb6760.jpg"
                      }
                    },
                    "hash": "Raftsundet_Norway_Hurtigruten_6de3bb6760",
                    "ext": ".jpg",
                    "mime": "image/jpeg",
                    "size": 5273.59,
                    "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/Raftsundet_Norway_Hurtigruten_6de3bb6760.jpg",
                    "previewUrl": null,
                    "provider": "aws-s3",
                    "provider_metadata": null,
                    "createdAt": "2023-12-06T23:07:39.731Z",
                    "updatedAt": "2023-12-06T23:07:39.731Z"
                  }
                },
                {
                  "id": 212,
                  "attributes": {
                    "name": "Norway Northern Lights Hurtigruten",
                    "alternativeText": "Norway Northern Lights Hurtigruten",
                    "caption": null,
                    "width": 7298,
                    "height": 4871,
                    "formats": {
                      "thumbnail": {
                        "name": "thumbnail_Norway Northern Lights Hurtigruten",
                        "hash": "thumbnail_Norway_Northern_Lights_Hurtigruten_05bc2221be",
                        "ext": ".JPG",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 234,
                        "height": 156,
                        "size": 5.42,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_Norway_Northern_Lights_Hurtigruten_05bc2221be.JPG"
                      },
                      "medium": {
                        "name": "medium_Norway Northern Lights Hurtigruten",
                        "hash": "medium_Norway_Northern_Lights_Hurtigruten_05bc2221be",
                        "ext": ".JPG",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 750,
                        "height": 500,
                        "size": 38.86,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_Norway_Northern_Lights_Hurtigruten_05bc2221be.JPG"
                      },
                      "small": {
                        "name": "small_Norway Northern Lights Hurtigruten",
                        "hash": "small_Norway_Northern_Lights_Hurtigruten_05bc2221be",
                        "ext": ".JPG",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 500,
                        "height": 333,
                        "size": 19.24,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_Norway_Northern_Lights_Hurtigruten_05bc2221be.JPG"
                      },
                      "large": {
                        "name": "large_Norway Northern Lights Hurtigruten",
                        "hash": "large_Norway_Northern_Lights_Hurtigruten_05bc2221be",
                        "ext": ".JPG",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 1000,
                        "height": 667,
                        "size": 66.93,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_Norway_Northern_Lights_Hurtigruten_05bc2221be.JPG"
                      }
                    },
                    "hash": "Norway_Northern_Lights_Hurtigruten_05bc2221be",
                    "ext": ".JPG",
                    "mime": "image/jpeg",
                    "size": 6163.63,
                    "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/Norway_Northern_Lights_Hurtigruten_05bc2221be.JPG",
                    "previewUrl": null,
                    "provider": "aws-s3",
                    "provider_metadata": null,
                    "createdAt": "2023-12-06T23:07:46.765Z",
                    "updatedAt": "2024-03-13T16:15:33.450Z"
                  }
                },
                {
                  "id": 137,
                  "attributes": {
                    "name": "hurtigen river cruise.jpg",
                    "alternativeText": null,
                    "caption": null,
                    "width": 2000,
                    "height": 1333,
                    "formats": {
                      "thumbnail": {
                        "name": "thumbnail_hurtigen river cruise.jpg",
                        "hash": "thumbnail_hurtigen_river_cruise_036330481e",
                        "ext": ".jpg",
                        "mime": "image/jpeg",
                        "path": null,
                        "width": 234,
                        "height": 156,
                        "size": 6.38,
                        "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_hurtigen_river_cruise_036330481e.jpg"
                      }
                    },
                    "hash": "hurtigen_river_cruise_036330481e",
                    "ext": ".jpg",
                    "mime": "image/jpeg",
                    "size": 284.93,
                    "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/hurtigen_river_cruise_036330481e.jpg",
                    "previewUrl": null,
                    "provider": "aws-s3",
                    "provider_metadata": null,
                    "createdAt": "2023-11-05T10:49:19.954Z",
                    "updatedAt": "2023-11-05T10:49:19.954Z"
                  }
                }
              ]
            },
            "logo": {
              "data": {
                "id": 311,
                "attributes": {
                  "name": "Hurtigruten Coastal logo",
                  "alternativeText": "Hurtigruten Coastal logo",
                  "caption": null,
                  "width": 5680,
                  "height": 2024,
                  "formats": {
                    "thumbnail": {
                      "name": "thumbnail_HR_logo_main_POS_RGB.png",
                      "hash": "thumbnail_HR_logo_main_POS_RGB_f5b6e7c9f7",
                      "ext": ".png",
                      "mime": "image/png",
                      "path": null,
                      "width": 245,
                      "height": 87,
                      "size": 3.67,
                      "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_HR_logo_main_POS_RGB_f5b6e7c9f7.png"
                    },
                    "large": {
                      "name": "large_HR_logo_main_POS_RGB.png",
                      "hash": "large_HR_logo_main_POS_RGB_f5b6e7c9f7",
                      "ext": ".png",
                      "mime": "image/png",
                      "path": null,
                      "width": 1000,
                      "height": 356,
                      "size": 16.78,
                      "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/large_HR_logo_main_POS_RGB_f5b6e7c9f7.png"
                    },
                    "small": {
                      "name": "small_HR_logo_main_POS_RGB.png",
                      "hash": "small_HR_logo_main_POS_RGB_f5b6e7c9f7",
                      "ext": ".png",
                      "mime": "image/png",
                      "path": null,
                      "width": 500,
                      "height": 178,
                      "size": 7.93,
                      "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/small_HR_logo_main_POS_RGB_f5b6e7c9f7.png"
                    },
                    "medium": {
                      "name": "medium_HR_logo_main_POS_RGB.png",
                      "hash": "medium_HR_logo_main_POS_RGB_f5b6e7c9f7",
                      "ext": ".png",
                      "mime": "image/png",
                      "path": null,
                      "width": 750,
                      "height": 267,
                      "size": 12.21,
                      "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/medium_HR_logo_main_POS_RGB_f5b6e7c9f7.png"
                    }
                  },
                  "hash": "HR_logo_main_POS_RGB_f5b6e7c9f7",
                  "ext": ".png",
                  "mime": "image/png",
                  "size": 38.29,
                  "url": "https://project-cruise.s3.eu-west-2.amazonaws.com/HR_logo_main_POS_RGB_f5b6e7c9f7.png",
                  "previewUrl": null,
                  "provider": "aws-s3",
                  "provider_metadata": null,
                  "createdAt": "2024-02-28T16:33:41.840Z",
                  "updatedAt": "2024-02-28T16:34:04.915Z"
                }
              }
            },
            "seo": null,
            "calloutbox": null,
            "blocks": []
          }
        }
      ]
    },
    "checklist": checkList
  }
];



