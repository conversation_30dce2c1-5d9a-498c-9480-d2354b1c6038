import { createSlice, PayloadAction } from '@reduxjs/toolkit';


interface SSORedirectState {
  page: string;
  data: any;
}

const initialState: SSORedirectState = {
  page: '/',
  data: {}
};

const ssoRedirectSlice = createSlice({
  name: 'setting',
  initialState,
  reducers: {
    setSSOData: (state, action: PayloadAction<any>) => {
      state.page = action.payload.page;
      state.data = action.payload.data;
    }
  },
});

export const { setSSOData } = ssoRedirectSlice.actions;

export default ssoRedirectSlice.reducer;
