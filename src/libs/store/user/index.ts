import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { signOut } from 'next-auth/react';
import router from 'next/router';
import { RootState } from '../store';

interface UserState {
  id: number;
  loggedIn: boolean;
  firstname: string;
  lastname: string;
  email: string;
  jwt: string;
  expires: string;
}

const initialState: UserState = {
  id: 0,
  loggedIn: false,
  firstname: '',
  lastname: '',
  email: '',
  jwt: '',
  expires: '',
};

// Async action to handle user logout
export const logoutUser: any = createAsyncThunk<
  void,
  void,
  { state: RootState }
>('user/logout', async (_, { dispatch }) => {
  localStorage.removeItem('token');
  const response = await signOut({
    callbackUrl: '/',
    redirect: false,
  });
  router.push(response.url);
  dispatch(userSlice.actions.resetUser()); // Dispatch resetUser action after successful logout
});

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    loginUser(state, action) {
      return {
        ...state,
        ...action.payload,
        loggedIn: true,
      };
    },
    resetUser(state) {
      return {
        ...state,
        ...initialState,
        loggedIn: false,
      };
    },
  },
});

export const { loginUser, resetUser } = userSlice.actions;

export default userSlice.reducer;
