import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface SettingState {
  loginModal: boolean;
  discountModal: {
    isModalOpen: boolean;
    data: any;
  };
  dwellModal: {
    isModalOpen: boolean,
    data: any,
  };
}

const initialState: SettingState = {
  loginModal: false,
  discountModal: {
    isModalOpen: false,
    data: {},
  },
  dwellModal: {
    isModalOpen: false,
    data: {},
  },
};

const settingSlice = createSlice({
  name: 'setting',
  initialState,
  reducers: {
    toggleLoginModal: (state) => {
      state.loginModal = !state.loginModal;
    },
    toggleDiscountModal: (state, action: PayloadAction<any>) => {
      state.discountModal.isModalOpen = action.payload.isModalOpen;
      state.discountModal.data = action.payload.data;
    },
    setDiscountData: (state, action: PayloadAction<any>) => {
      state.discountModal.data = action.payload;
    },
    toggleDWellModal: (state, action: PayloadAction<any>) => {
      state.dwellModal.isModalOpen = !state.dwellModal.isModalOpen;
      state.dwellModal.data = action.payload;
    },
  },
});

export const { toggleLoginModal, toggleDiscountModal, setDiscountData, toggleDWellModal } = settingSlice.actions;

export default settingSlice.reducer;
