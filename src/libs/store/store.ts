import rootReducer from './reducers';
import { configureStore } from '@reduxjs/toolkit';
import { createWrapper } from 'next-redux-wrapper';
import localForage from 'localforage';
import {
  FLUSH,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
  REHYDRATE,
} from 'redux-persist';

const store = () => {
  if (typeof window === 'undefined') {
    return configureStore({
      reducer: rootReducer,
      middleware: (getDefaultMiddleware) => getDefaultMiddleware(),
    });
  }
  const { persistStore, persistReducer } = require('redux-persist');

  const name = 'cruise-collective';
  const storeIndex = localForage.createInstance({
    driver: localForage.INDEXEDDB,
    name,
    version: 1.0,
    storeName: name,
    description: 'Cruise Collective',
  });

  const persistConfig = {
    key: name,
    whitelist: ['user', 'ssoRedirect'],
    storage: storeIndex,
  };

  const persistedReducer = persistReducer(persistConfig, rootReducer);
  // Create a new reducer with our existing reducer

  const store = configureStore({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        },
      }),
  }) as any;

  store.__persistor = persistStore(store); // This creates a persistor object & push that persisted object to .__persistor, so that we can avail the persistability feature
  return store;
};

export const getStore = store();
// Export the wrapper & wrap the pages/_app.js with this wrapper only
export const wrapper = createWrapper(store);

export const state = () => getStore.getState();
export type RootState = ReturnType<typeof getStore.getState>;
export type AppDispatch = typeof getStore.dispatch;
export type AppStore = typeof getStore;

export default store;
