import { createSlice, PayloadAction } from '@reduxjs/toolkit';


interface ApiState {
homeData : any
}

const initialState: ApiState = {
    homeData: {},
};

const apiSlice = createSlice({
  name: 'api',
  initialState,
  reducers: {
    setHomeData: (state, action: PayloadAction<any>) => {
      state.homeData = action.payload;
    },
  },
});

export const { setHomeData } = apiSlice.actions;


export default apiSlice.reducer
