import ArticleBlock from '@/components/PostPageContent/ArticleBlock';
import ArticleCTA from '@/components/CTABanner/ArticleCTA';
import CallToAction from '@/components/PostPageContent/CallToAction';
import EmbedCode from '@/components/PostPageContent/EmbedCode';
import ExploreMoreBySeaBlock from '@/components/PostPageContent/ExploreMoreBySeaBlock';
import FancyImageBlock from '@/components/PostPageContent/FancyImageBlock';
import ImageBlock from '@/components/PostPageContent/ImageBlock';
import JoinUsImageBlock from '@/components/PostPageContent/JoinUsImageBlock';
import InfoBoxSlider from '@/components/PostPageContent/InfoBoxSlider';
import Promo2Section from '@/components/PostPageContent/Promo2Section';
import PromoSection from '@/components/PostPageContent/PromoSection';
import TestimonialBlock from '@/components/PostPageContent/TestimonialBlock';
import TextBlock from '@/components/PostPageContent/TextBlock';
import RichTextBlock from '@/components/PostPageContent/RichTextBlock';
import JoinUsRichTextBlock from '@/components/PostPageContent/JoinUsRichTextBlock';

// Component map for mapping component types to React components
const componentMap = {
  'blocks.text-block': TextBlock,
  'blocks.rich-text': RichTextBlock,
  'blocks.join-us-rich-text': JoinUsRichTextBlock,
  'blocks.promo': PromoSection,
  'blocks.promo2': Promo2Section,
  'blocks.fancy-image-section': FancyImageBlock,
  'blocks.call-to-actions': CallToAction,
  'blocks.testimonial': TestimonialBlock,
  'blocks.info-box-slider': InfoBoxSlider,
  'blocks.related-articles': ArticleBlock,
  'blocks.explore-more-by-sea': ExploreMoreBySeaBlock,
  'blocks.image-block': ImageBlock,
  'blocks.join-us-image-block': JoinUsImageBlock,
  'blocks.embed-code': EmbedCode,
  'blocks.article-cta': ArticleCTA,
};

const RenderComponent = ({
  blocks,
  componentType = '',
  sort = 0,
  page = '',
  ...props
}) => {
  // Filter blocks to get only those that match the component type
  let filteredBlocks;
  if (sort) {
    filteredBlocks = blocks.filter(
      (block) => block.__component === componentType && block.sort === sort,
    );
  } else {
    filteredBlocks = blocks.filter(
      (block) => block.__component === componentType,
    );
  }
  // Get the component from the map
  const Component = componentMap[componentType];

  // componentID without blocks.
  const componentID = componentType.split('.').pop();

  // Check if the component exists and there are matching blocks
  if (!Component || filteredBlocks.length === 0) {
    // return <p>Component not found or no instances available.</p>;
    return <p></p>;
  }

  // Pass the filtered blocks (all instances) to the component
  if (page === 'destination' || page === 'cruise-line' || page === 'interest') {
    return <Component data={filteredBlocks} page={page} {...props} />;
  }

  return <Component data={filteredBlocks} id={componentID} {...props} />;
};

export default RenderComponent;
