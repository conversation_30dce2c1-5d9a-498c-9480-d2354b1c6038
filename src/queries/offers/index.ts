import axios from 'axios';
import { baseUrl } from '../../utils';

export const defaultParams = `?populate=*&&sort[0]=createdAt%3Adesc&filters[content_type][name][$eq]=`;

export const getOffers = async (pageIndex, pageSize) => {
  try {
    const response = await axios.get(
      `${baseUrl}/api/offers?populate[featured_image]=*&pagination[page]=${pageIndex}&pagination[pageSize]=${pageSize}&populate[cruise_line][populate][0]=logo`,
    );
    return response.data;
  } catch (err) {
    console.error(err);
    throw err;
  }
};
