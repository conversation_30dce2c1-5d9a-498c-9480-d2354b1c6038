import axios from 'axios';
import { getContents } from './content/index';
import { baseUrl } from '@/utils';

const fetchData = async (endpoint, pageIndex, pageSize, type) => {
  const { slug, content } = type;
  const filterType = slug ? `&filters[${content}][slug][$eq]=${slug}` : '';
  try {
    const response = await axios.get(
      `${baseUrl}/api/${endpoint}?populate=*&sort[0]=createdAt%3Adesc&pagination[page]=${pageIndex}&pagination[pageSize]=${pageSize}${filterType}`,
    );
    return response.data;
  } catch (err) {
    console.error(err);
    throw err; // throw the error to be caught by the caller
  }
};

export const getDestinations = async (pageIndex, pageSize, type) => {
  return fetchData('destinations', pageIndex, pageSize, type);
};

export const getArticles = async (pageIndex, pageSize, type) => {
  // return fetchData('insiprations', pageIndex, pageSize, type);
  // populate[author_info][populate]=*
  // populate[explore_more][populate]=*
  // populate[featured_image][populate]=*
  // populate[inspiration_category][populate]=*
  // populate[related_articles][populate]=*

  const { slug, content } = type;
  const filterType = slug ? `&filters[${content}][slug][$eq]=${slug}` : '';
  try {
    const response = await axios.get(
      `${baseUrl}/api/insiprations?populate[author_info][populate]=*&populate[featured_image]populate=*&populate[partnership][populate]=*&sort[0]=date%3Adesc&sort[1]=updatedAt%3Adesc&sort[2]=createdAt%3Adesc&pagination[page]=${pageIndex}&pagination[pageSize]=${pageSize}${filterType}`,
    );
    return response.data;
  } catch (err) {
    console.error(err);
    throw err; // throw the error to be caught by the caller
  }
};

export const getInterests = async (pageIndex, pageSize, type) => {
  const response = await axios.get(
    `${baseUrl}/api/interests?populate[calloutbox][populate]=*&populate[featured_image]populate=*&sort[0]=createdAt%3Adesc&pagination[page]=${pageIndex}&pagination[pageSize]=${pageSize}&filters[id][$ne]=54`,
  );

  return response.data;
};

export const getCruiseLines = async (pageIndex, pageSize, type) => {
  const response = await axios.get(
    `${baseUrl}/api/cruise-lines?populate[calloutbox][populate]=*&populate[featured_image]populate=*&populate[logo]populate=*&sort[0]=createdAt%3Adesc&pagination[page]=${pageIndex}&pagination[pageSize]=${pageSize}`,
  );

  return response.data;
};

export const getAllCategoriesDropdown = async (
  endpoint,
  pageIndex = 1,
  pageSize = 25,
) => {
  try {
    const response = await axios.get(
      `${baseUrl}/api/${endpoint}?populate=*&sort[0]=createdAt%3Adesc&pagination[page]=${pageIndex}&pagination[pageSize]=${pageSize}`,
    );
    return response.data;
  } catch (err) {
    console.error(err);
    throw err; // throw the error to be caught by the caller
  }
};

export const getAllArticleCategories = async () => {
  return getAllCategoriesDropdown('inspiration-categories', 1, 100);
};

export const getDestinationCategories = async () => {
  return getAllCategoriesDropdown('destination-categories', 1, 100);
};

export const getInterestCategories = async () => {
  return getAllCategoriesDropdown('interest-categories', 1, 100);
};

export const getCruiseLineCategories = async () => {
  return getAllCategoriesDropdown('cruise-line-categories', 1, 100);
};

const getRegistrationData = async () => {
  const response = await axios.get(`${baseUrl}/api/regdata`);
  return response.data;
};

export const postRegister = async (data) => {
  try {
    data.role = '1';
    data.username = data.email;

    const url = `${baseUrl}/api/auth/local/register`;
    const response = await axios.post(url, data);

    return response;
  } catch (error) {
    return false;
  }
};

export const updateUser = async (data, id) => {
  try {
    data.username = data.email;
    const url = `${baseUrl}/api/users/${id}`;
    const response = await axios.put(url, data);
    return response;
  } catch (error) {
    return false;
  }
};

export const deleteUser = async (data, id) => {
  try {
    const url = `${baseUrl}/api/users/${id}`;
    const response = await axios.delete(url, data);
    return response;
  } catch (error) {
    return false;
  }
};

const getHomePageData = async () => {
  try {
    const response = await axios.get(
      `${baseUrl}/api/homepage?populate[category_promo][populate]=*&populate[promos]=*&populate[saving_block_promos]=*&populate[sliders][populate]=image&populate[cta2][populate]=checklist&populate[articles][populate]=featured_image&populate[cta][populate]=cruise_lines.calloutbox.terms_and_condition&populate[cta][populate]=cruise_lines.logo&populate[cta][populate]=cruise_lines.calloutbox.how_to_use&populate[seo][populate]=*&populate[countdown][populate]=*`,
    );

    return response?.data?.data?.attributes;
  } catch (err) {
    console.error(err);
    throw err; // throw the error to be caught by the caller
  }
};

const getArticlesLandingPageData = async () => {
  try {
    const response = await axios.get(
      `${baseUrl}/api/aricles-landing-page?populate=*`,
    );

    return response?.data?.data?.attributes;
  } catch (err) {
    console.error(err);
    throw err; // throw the error to be caught by the caller
  }
};

const spacialOffers = async () => {
  try {
    const response = await axios.get(
      `${baseUrl}/api/black-friday-offer?populate[backgroundImage][populate]=*&populate[cruise_lines][populate]=calloutbox.how_to_use,featured_image,logo,calloutbox.terms_and_condition&populate[icon_image][populate]=*`,
    );

    return response?.data?.data?.attributes;
  } catch (err) {
    console.error(err);
    throw err; // throw the error to be caught by the caller
  }
};

const getCookiePopups = async () => {
  try {
    const response = await axios.get(`${baseUrl}/api/cookie-popups?populate=*`);
    return response.data;
  } catch (error) {
    // Handle error gracefully
    console.error('Failed to fetch cookie pop-ups:', error);
    throw error;
  }
};

const getTermsConditionData = async () => {
  try {
    const response = await axios.get(
      `${baseUrl}/api/terms-and-and-condition?populate=*`,
    );

    return response?.data?.data?.attributes;
  } catch (err) {
    console.error(err);
    throw err; // throw the error to be caught by the caller
  }
};

const getCompetitionTermsConditionData = async () => {
  try {
    const response = await axios.get(
      `${baseUrl}/api/competitions-terms-and-conditions?populate=*`,
    );

    return response?.data?.data?.attributes;
  } catch (err) {
    console.error(err);
    throw err; // throw the error to be caught by the caller
  }
};

export async function getDepartures() {
  const apiUrl = `${baseUrl}/api/departures/?populate=*`;

  try {
    const response = await axios.get(apiUrl);
    return response?.data.data;
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
}

export async function getAllDestinations() {
  const apiUrl = `${baseUrl}/api/destinations?populate=*&pagination[page]=1&pagination[pageSize]=1000`;

  try {
    const response = await axios.get(apiUrl);
    return response?.data.data;
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
}

export async function getSeasons() {
  const apiUrl = `${baseUrl}/api/seasons/?populate=*`;

  try {
    const response = await axios.get(apiUrl);
    return response?.data.data;
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
}

const forgotPasswordByEmail = async (email) => {
  try {
    const url = `${baseUrl}/api/auth/forgot-password`;
    const response = await axios.post(url, email);
    return response;
  } catch (error) {
    return false;
  }
};

const resetPasswordByLink = async (data) => {
  try {
    const url = `${baseUrl}/api/auth/reset-password`;
    const response = await axios.post(url, data);
    return response;
  } catch (error) {
    return false;
  }
};

const sendEmailConfirmation = async (email) => {
  try {
    const url = `${baseUrl}/api/auth/send-email-confirmation`;
    const response = await axios.post(url, { email });
    return response.data;
  } catch (error) {
    return false;
  }
};

const registerEmailConfirmation = async (query) => {
  try {
    const url = `${baseUrl}/api/auth/email-confirmation?confirmation=${query}`;
    const response = await axios.get(url);
    return response;
  } catch (error) {
    return false;
  }
};

const getUserDetailById = async (id: number) => {
  try {
    const url = `${baseUrl}/api/users/${id}?populate=*`;
    const response = await axios.get(url);
    return response;
  } catch (error) {
    return false;
  }
};

const postNewsLatterSignUp = async (data) => {
  const reqBody = { data: { email: data.email } };
  try {
    const url = `${baseUrl}/api/newsletter-signups`;
    const response = await axios.post(url, reqBody);
    return response;
  } catch (error) {
    return false;
  }
};

export const getTravelPartner = async (pageIndex, pageSize) => {
  try {
    const response = await axios.get(
      `${baseUrl}/api/travel-partner-offers?populate[author_info][populate]=*&populate[featured_image]populate=*&populate[logo][populate]=*&sort[0]=title%3Aasc&pagination[page]=${pageIndex}&pagination[pageSize]=${pageSize}`,
    );

    return response.data;
  } catch (err) {
    console.error(err);
    throw err; // throw the error to be caught by the caller
  }
};

export const getAllTravelPartner = async () => {
  try {
    const response = await axios.get(
      `${baseUrl}/api/travel-partner-offers?populate=*&sort[0]=title%3Aasc&pagination[page]=1&pagination[pageSize]=10000`,
    );

    return response.data;
  } catch (err) {
    console.error(err);
    throw err; // throw the error to be caught by the caller
  }
};

 const getJoinUse = async (slug: string) => {
  try {
    const response = await axios.get(
      `${baseUrl}/api/join-uses?filters[slug][$eq]=${slug}&populate[blocks][populate]=*&populate[images][populate]=*&populate=*`,
    );
    return response.data;
  } catch (err) {
    console.error(err);
    throw err; // throw the error to be caught by the caller
  }
};

 const getPuzzleData = async () => {
  try {
    const response = await axios.get(`${baseUrl}/api/puzzles`);
    return response.data;
  } catch (err) {
    console.error(err);
    throw err; // throw the error to be caught by the caller
  }
};

export {
  getContents,
  getRegistrationData,
  getHomePageData,
  getArticlesLandingPageData,
  forgotPasswordByEmail,
  resetPasswordByLink,
  getCookiePopups,
  getUserDetailById,
  registerEmailConfirmation,
  sendEmailConfirmation,
  getTermsConditionData,
  postNewsLatterSignUp,
  getCompetitionTermsConditionData,
  spacialOffers,
  getJoinUse,
  getPuzzleData,
};
