import axios from 'axios';

export const userExist = async (email_address: string) => {
  try {
    const response = await axios.post(
      '/zephr/media/user/info',
      {
        identifiers: { email_address },
      },
      {
        headers: { 'Content-Type': 'application/json' },
      },
    );

    if (response.status === 200) return true;
  } catch (error) {
    return false;
  }
};

export async function requestResetCode(email) {
  try {
    const response = await axios.post(`/zephr/users/reset`, {
      identifiers: {
        email_address: email,
      },
    });
    return response.data; // Handle the response if needed
  } catch (error) {
    console.error('Error requesting reset code:', error);
    throw error;
  }
}

export async function verifyAuthCode(email, code) {
  try {
    const response = await axios.post(`/zephr/users/reset/${code}`, {
      identifiers: {
        email_address: email,
      },
    });
    return response.data; // Handle the response if needed
  } catch (error) {
    console.error('Error verifying authentication code:', error);
    throw error;
  }
}

export async function resetPassword(email, code, newPassword) {
  try {
    const response = await axios.post(`/zephr/users/reset/${code}`, {
      identifiers: {
        email_address: email,
      },
      validators: {
        password: newPassword,
      },
    });
    return response.data; // Handle the response if needed
  } catch (error) {
    console.error('Error resetting password:', error);
    throw error;
  }
}
