import React, { useMemo } from 'react';
import Header, { HeaderOptions } from '@/layout/Header/Header';
import Footer, { FooterOptions } from '@/layout/Footer/Footer';

import PageNotFound from '@/components/Shared/PageNotFound';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import CookiesModal from '@/components/Modal/CookiesModal';
import { poppins } from '@/utils/fonts';
import SignInModal from '@/components/SignInModal';
import DisCountModal from '@/components/Modal/DisCountModal';
import DwellPopup from '@/components/Modal/DwellPopup';
import Script from 'next/script';
interface IMasterOptions {
  header: Partial<HeaderOptions>;
  footer: Partial<FooterOptions>;
}

export type MasterOptions = Partial<IMasterOptions>;

export interface IMasterProps {
  children?: React.ReactNode | React.ReactNode[];
  masterOptions?: MasterOptions;
  pageProps: any;
}
const Master: React.FC<IMasterProps> = (props) => {
  const { children, pageProps, masterOptions } = props;

  const isErrorPage = useMemo(() => {
    return Boolean(pageProps?.statusCode && pageProps?.statusCode !== 200);
  }, [pageProps.statusCode]);

  // if (isErrorPage) return <>{children}</>;
  if (isErrorPage) return <PageNotFound />;

  // if (status !== "authenticated") return <LoginPage />;

  return (
    <div className={`${poppins}`}>
      <Header options={masterOptions?.header || {}} />
      <Script
        id="fb-pixel"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
          !function(f,b,e,v,n,t,s)
          {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
          n.callMethod.apply(n,arguments):n.queue.push(arguments)};
          if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
          n.queue=[];t=b.createElement(e);t.async=!0;
          t.src=v;s=b.getElementsByTagName(e)[0];
          s.parentNode.insertBefore(t,s)}(window, document,'script',
          'https://connect.facebook.net/en_US/fbevents.js');
          fbq('init', ${process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID}); 
          fbq('track', 'PageView');
        `,
        }}
      />

      {children}

      <Footer options={masterOptions?.footer || {}} />
      <CookiesModal />
      <ToastContainer />
      <SignInModal />
      <DisCountModal />
      <DwellPopup />
    </div>
  );
};

export default Master;
