import React, { useEffect, useRef, useState, MouseEvent } from 'react';
import clsx from 'clsx';
import useAppRouter from '@/hooks/useAppRouter';
import SearchInput from '@/components/SearchInput';
import UserStatus from '@/components/UserStatus';

import useIsNavHideForSmallDevice from '@/hooks/useIsNavHideForSmallDevice';
import NavbarItem from './NavbarItem';
import NavRight from '@/assets/svg/nav-right.svg';
import { useNavigation } from '@/contexts/NavigationProvider';

interface NavbarProps {
  isDrawerOpen: boolean;
  setIsDrawerOpen: (isOpen: boolean) => void;
}

const Navbar: React.FC<NavbarProps> = ({ isDrawerOpen, setIsDrawerOpen }) => {
  const isTabletOrBelowDevice = useIsNavHideForSmallDevice();
  const router = useAppRouter();
  const navigation = useNavigation();
  const [isSearchBarHide, setIsSearchBarHide] = useState(true);
  const [currentMenu, setCurrentMenu] = useState<any>(null);
  const newRef = useRef<HTMLDivElement>(null);
  const items = navigation || [];

  const handleOutsideClick = (e: MouseEvent<Document>) => {
    if (
      newRef.current &&
      !newRef.current.contains(e.target as Node) &&
      !isSearchBarHide
    ) {
      setIsSearchBarHide(true);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleOutsideClick as any);
    return () =>
      document.removeEventListener('mousedown', handleOutsideClick as any);
  }, [isSearchBarHide]);

  return (
    <div
      ref={newRef}
      className={`flex ${
        !isTabletOrBelowDevice ? '' : isDrawerOpen ? 'h-screen' : ''
      } container mx-auto w-full`}
    >
      {isSearchBarHide && (
        <nav
          className={clsx('text-[#36453b]', {
            'hidden lg:flex w-full': !isTabletOrBelowDevice,
            'flex flex-col lg:hidden w-full': isTabletOrBelowDevice,
          })}
        >
          {/* {isDrawerOpen && <div className='py-6'><MobileSearchInput /></div>} */}
          <ul
            className={clsx('flex w-full', {
              'justify-between items-center': !isTabletOrBelowDevice,
              'flex-col w-full pb-2 z-50 pt-10': isTabletOrBelowDevice,
              block: isTabletOrBelowDevice && isDrawerOpen,
              hidden: isTabletOrBelowDevice && !isDrawerOpen,
            })}
          >
            {currentMenu && (
              <>
                <li className="block lg:hidden border-b text-lg border-[#DCDAD6] py-4 font-medium">
                  {currentMenu[0].label}
                </li>
                <li
                  className="block lg:hidden text-sm text-orange mt-4"
                  onClick={() => setCurrentMenu(null)}
                >
                  <NavRight className="inline-block rotate-180 mr-2" /> BACK
                </li>
              </>
            )}

            {currentMenu ? (
              <div className="ms-6">
                {currentMenu?.map((item) => (
                  <NavbarItem
                    key={`nav-item-${item.id}`}
                    {...item}
                    isTabletOrBelowDevice={isTabletOrBelowDevice}
                    setIsDrawerOpen={setIsDrawerOpen}
                    isMobileDrawer={isTabletOrBelowDevice && isDrawerOpen}
                    isOpenSubMenu={true}
                  />
                ))}
              </div>
            ) : (
              items.map((item, itemIdx) => (
                <NavbarItem
                  key={`nav-item-${item.id}-${itemIdx}`}
                  {...item}
                  isActive={
                    item.matcher
                      ? item.matcher.test(router.asPath) ||
                        router.isSame(item.href || '')
                      : item.href
                      ? router.isSame(item.href)
                      : false
                  }
                  setIsDrawerOpen={setIsDrawerOpen}
                  isTabletOrBelowDevice={isTabletOrBelowDevice}
                  isMobileDrawer={isTabletOrBelowDevice && isDrawerOpen}
                  currentMenu={currentMenu}
                  setCurrentMenu={setCurrentMenu}
                />
              ))
            )}
            <li className="block lg:hidden mt-[140px] ">
              <UserStatus setIsDrawerOpen={setIsDrawerOpen} />
            </li>
            {/* <li
              className="cursor-pointer items-center hidden lg:flex"
              onClick={() => setIsSearchBarHide(!isSearchBarHide)}
            >
              {isSearchBarHide ? <SearchIcon /> : <CloseIcon />}
            </li> */}
          </ul>
        </nav>
      )}
      {!isSearchBarHide && (
        <div className="w-full hidden lg:block h-[49px]">
          <SearchInput />
        </div>
      )}
    </div>
  );
};

export default Navbar;
