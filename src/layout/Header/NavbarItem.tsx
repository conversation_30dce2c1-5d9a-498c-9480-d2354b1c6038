import React, { useEffect, useMemo, useState, MouseEvent } from 'react';
import Link from 'next/link';
import clsx from 'clsx';
import { useRouter } from 'next/router';
import NavRight from '@/assets/svg/nav-right.svg';
import { NavbarItemProps, SubItem } from '@/types/navbar';
import useAppRouter from '@/hooks/useAppRouter';

const ArrowIcon = () => <span className="ml-2 text-lg">&#9662;</span>;

const NavbarItem: React.FC<NavbarItemProps> = ({
  isTabletOrBelowDevice,
  sub,
  isActive = false,
  isMobileDrawer,
  setIsDrawerOpen,
  currentMenu,
  setCurrentMenu,
  isOpenSubMenu = false,
  ...props
}) => {
  const router = useRouter();
  const { asPath } = useAppRouter();
  const [showChild, setShowChild] = useState(false);

  const className = useMemo(
    () =>
      isActive
        ? `text-orange px-2 py-1 ${isTabletOrBelowDevice ? '' : 'bg-white'} `
        : 'text-black px-2 py-1',
    [isActive],
  );
  const handleChildClick = (child: SubItem) => (event: MouseEvent) => {
    event.preventDefault();

    if (isTabletOrBelowDevice) {
      if (child.sub && child.sub.length > 0) {
        setShowChild((prev) => !prev);
      } else {
        setIsDrawerOpen(false);
        router.push(child.href || '#');
      }
    } else if (child.href) {
      router.push(child.href);
    }
  };

  useEffect(() => {
    setShowChild(false);
  }, [router.pathname]);

  useEffect(() => {
    if (isActive) setShowChild(true);
  }, [isActive]);

  const renderSubChild = (children?: SubItem[]) => {
    if (!children) return null;

    return (
      <div
        className={clsx({
          hidden: isTabletOrBelowDevice && !showChild,
          'hidden group-hover:block absolute top-full show-dd-shadow':
            !isTabletOrBelowDevice,
        })}
      >
        <ul
          className={clsx(
            'flex flex-col text-xs leading-6 tracking-[2.4px] font-normal pt-5 pl-5  px-1 w-full',
            {
              'relative z-30 gap-5 min-w-[20.5rem] pt-[1.625rem] p-5 pb-[1.125rem] max-w-[15.625rem] show-shadow bg-white':
                !isTabletOrBelowDevice,
              'bg-[#F5F2EE] gap-3 lg:border-b-2 border-orange':
                isTabletOrBelowDevice,
            },
          )}
        >
          {children.map((subItem, index) => (
            <div
              key={`sub-${index}`}
              onClick={(event) => {
                if (isTabletOrBelowDevice) {
                  event.stopPropagation();
                }
                handleChildClick(subItem)(event);
              }}
            >
              <Link href={subItem.href || '#'} passHref>
                <li
                  className={`${
                    asPath === subItem.href ? 'text-orange' : 'text-black'
                  } text-sm font-medium leading-tight uppercase hover:text-brand`}
                >
                  <span className="text-sm">{subItem.label}</span>
                  {subItem.sub && subItem.sub?.length > 0 && <ArrowIcon />}
                </li>
              </Link>
            </div>
          ))}
        </ul>
      </div>
    );
  };

  if (!sub || !sub.length) {
    return (
      <div
        className={clsx({
          'py-2 border-b border-[#DCDAD6] hover:text-orange': isMobileDrawer,
        })}
      >
        <Link href={props.href} passHref>
          {/* isOpenSubMenu */}
          <li
            className={clsx(
              `font-medium leading-tight uppercase hover:text-orange ${className}`,
              {
                'text-lg': isMobileDrawer && !isOpenSubMenu,
                'text-sm': (!isMobileDrawer && !isOpenSubMenu) || isOpenSubMenu,
              },
            )}
            onClick={handleChildClick(props as SubItem)}
          >
            <span>{props.label}</span>
          </li>
        </Link>
      </div>
    );
  }

  return (
    <div
      onClick={() =>
        isTabletOrBelowDevice &&
        setCurrentMenu([
          {
            ...props,
          },
          ...sub,
        ])
      }
      className={clsx('relative h-full group flex hover:bg-white', {
        // 'border-b': showChild,
        'py-2 border-b border-[#DCDAD6] z-1000 text-sm leading-6 tracking-[2.4px]':
          isTabletOrBelowDevice,
      })}
    >
      <Link href={props.href} passHref className="flex w-full">
        <span
          className={`${className} flex justify-between items-center w-full text-lg lg:text-sm font-medium leading-tight uppercase hover:brand`}
        >
          {props.label}
          <NavRight className="ml-2 lg:rotate-90" />
        </span>
      </Link>
      {!isTabletOrBelowDevice && renderSubChild(sub)}
    </div>
  );
};

export default NavbarItem;
