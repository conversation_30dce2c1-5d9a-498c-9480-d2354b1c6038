import React, { useEffect, useRef, useState } from 'react';
import Navbar from '@/layout/Header/Navbar';
import Logo from '@/assets/svg/logo.svg';
import Link from 'next/link';
import UserStatus from '@/components/UserStatus';
import CloseIcon from '@/components/Shared/CloseIcon';
import { successModalDto } from '@/Interface/Dto';
import SuccessfulModal from '@/components/Modal/SuccessfulModal';
import useIsNavHideForSmallDevice from '@/hooks/useIsNavHideForSmallDevice';
import clsx from 'clsx';
import { toggleDWellModal } from '@/libs/store/setting';
import { useAppDispatch, useAppSelector } from '@/libs/hooks';
export interface HeaderOptions {
  actionBtnIsFilled?: boolean;
}
export interface IHeaderProps {
  options?: Partial<HeaderOptions>;
}
const Header: React.FC<IHeaderProps> = () => {
  const user = useAppSelector((state) => state.user);
  const login = useAppSelector((state) => state.setting.loginModal);
  const dispatch = useAppDispatch();
  const [showSuccessModal, setShowSuccessModal] = useState<successModalDto>({});
  const [isDrawerOpen, setIsDrawerOpen] = useState<boolean>(false);
  const isTabletOrBelowDevice = useIsNavHideForSmallDevice();
  const modalOpened = useRef(false); 

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;

    const handleMouseMove = () => {
      if (!modalOpened.current && !user.loggedIn && !login) {
        modalOpened.current = true; // Set to true so the modal will open only once
        // Open the modal after 3 seconds of mouse movement
        timer = setTimeout(() => {
          dispatch(toggleDWellModal(true));
        }, 10000); // 10 seconds delay before opening the modal
      }
    };

    // Listen for mouse movement
    window.addEventListener('mousemove', handleMouseMove);

    // Cleanup event listener and timeout when the component unmounts
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      if (timer) clearTimeout(timer);
    };
  }, [user.loggedIn, login, dispatch]);



  return (
    <div className={clsx( 'container mx-auto px-4 md:px-10', { 'bg-white': isTabletOrBelowDevice && isDrawerOpen })}>
      <div
        className={clsx(
          'py-5 md:py-6',
          { 'bg-white': isTabletOrBelowDevice && isDrawerOpen },
          { 'border-b border-[#DCDAD6]': isTabletOrBelowDevice && !isDrawerOpen },
        )}
      >
        <header className="flex justify-between items-center md:mb-6">
          {/* Mobile screen search icon ended here */}
          {/* Logo image */}
          <div>
            <Link href="/">
              <Logo
                width="382"
                height="22"
                viewBox="0 0 500 29"
                className="hidden md:block"
              />
              <Logo
                className="md:hidden mr-1"
                width="252"
                height="15"
                viewBox="0 0 500 29"
                // style={{ transform: 'scale(0.8)' }}
              />
            </Link>
          </div>
          {/* Logo image */}
          {/* Hamburger menu and close icon toggle started here */}
          <div
            className="lg:hidden"
            onClick={() => setIsDrawerOpen(!isDrawerOpen)}
          >
            {!isDrawerOpen && (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="w-6 h-6 cursor-pointer"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
                />
              </svg>
            )}
            {isDrawerOpen && (
              <div>
                <CloseIcon />
              </div>
            )}
          </div>
          {/* Hamburger menu and close icon toggle ended here */}
          <div className="hidden lg:block">
            <UserStatus setIsDrawerOpen={setIsDrawerOpen} />
          </div>
        </header>

        <Navbar isDrawerOpen={isDrawerOpen} setIsDrawerOpen={setIsDrawerOpen} />
      </div>

      {!!Object.keys(showSuccessModal).length && (
        <SuccessfulModal
          showSuccessModal={showSuccessModal}
          setShowSuccessModal={setShowSuccessModal}
        />
      )}
    </div>
  );
};
export default Header;
