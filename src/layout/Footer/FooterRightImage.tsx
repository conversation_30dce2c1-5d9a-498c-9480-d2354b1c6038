import React from 'react';

const FooterRightImage = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="92" height="93" viewBox="0 0 92 93" fill="none">
                <g opacity="0.8">
                  <path d="M14.3937 39.3479C16.1213 39.3479 17.8232 38.515 18.5929 36.4068C19.2856 34.498 18.7639 32.7368 17.1732 31.5829L17.0193 31.4701L16.0187 32.9711L16.1384 33.0752C17.1134 33.9515 17.3357 34.7149 16.9423 35.7908C16.6772 36.5195 16.1983 37.0401 15.5568 37.309C14.8556 37.5954 13.9661 37.5607 12.9912 37.1963C11.1268 36.5022 10.3143 35.0967 10.8873 33.535C11.2807 32.4592 11.9392 32.0254 13.2392 31.9994H13.4017L13.5898 30.1948L13.4017 30.1774C11.4432 30.0039 9.92947 31.0016 9.23674 32.919C8.15916 35.8862 10.1005 38.1419 12.3498 38.9835C12.9997 39.2264 13.701 39.3653 14.4023 39.3653L14.3937 39.3479Z" fill="#FF9A31"/>
                  <path d="M22.698 28.7198L19.5251 25.9088L20.0554 25.2928L24.9643 26.091L26.4011 24.4339L21.2698 23.601C21.7059 22.5078 21.4152 21.406 20.4573 20.5557C19.8244 19.9918 19.1146 19.7315 18.4133 19.8009C17.7035 19.8703 17.0193 20.2694 16.4207 20.9635L14.1372 23.6097L21.4836 30.134L22.698 28.7285V28.7198ZM16.6772 23.3754L17.7805 22.1001C17.9943 21.8571 18.2252 21.7097 18.4646 21.6749C18.7212 21.6402 18.9778 21.7444 19.2343 21.9699C19.8672 22.5252 19.6192 23.0805 19.3028 23.4448L18.1995 24.7202L16.6772 23.3668V23.3754Z" fill="#FF9A31"/>
                  <path d="M32.6185 16.2785L29.5482 10.8213L27.949 11.7496L31.0534 17.2675C31.3528 17.7881 31.4468 18.2739 31.3613 18.6904C31.2672 19.1155 30.9679 19.4712 30.4804 19.7575C29.2489 20.469 28.4878 19.7402 28.0687 19.0027L24.9642 13.4848L23.365 14.4131L26.4352 19.8703C26.9911 20.8594 27.7694 21.5101 28.6759 21.753C28.9325 21.8224 29.1976 21.8571 29.4713 21.8571C30.0956 21.8571 30.7284 21.6749 31.3528 21.3192C33.2342 20.226 33.7303 18.2566 32.627 16.2958L32.6185 16.2785Z" fill="#FF9A31"/>
                  <path d="M37.0791 8.39083L35.2979 8.84943L37.6981 18.4442L39.4794 17.9856L37.0791 8.39083Z" fill="#FF9A31"/>
                  <path d="M46.3876 11.3679C45.5409 10.9601 44.814 10.6044 44.8055 9.8843C44.8055 9.65005 44.8824 9.43315 45.0193 9.28565C45.1647 9.12081 45.3614 9.03405 45.6008 9.03405C46.0968 9.03405 46.4988 9.34639 46.8323 9.97973L46.9264 10.1619L48.4231 9.14684L48.3375 8.99935C47.7047 7.87147 46.7981 7.29886 45.5837 7.31621C44.8653 7.31621 44.2068 7.59384 43.7193 8.08837C43.2233 8.59158 42.9582 9.27698 42.9582 10.0144C42.9582 11.767 44.3265 12.4177 45.4127 12.9382C46.2508 13.3373 46.9692 13.6844 46.9777 14.3958C46.9777 15.1419 46.4817 15.6451 45.7291 15.6451C44.9081 15.6451 44.3351 15.0205 44.0101 13.7798L43.9502 13.5369L42.4279 14.5259L42.4536 14.6561C42.8384 16.3739 44.0956 17.4411 45.7291 17.4411C45.7291 17.4411 45.7377 17.4411 45.7462 17.4411C47.5849 17.4324 48.8592 16.157 48.8507 14.3437C48.8507 12.5912 47.4823 11.9318 46.3876 11.4026V11.3679Z" fill="#FF9A31"/>
                  <path d="M54.9057 14.1963L58.0529 15.1853L58.5746 13.4848L55.4274 12.4958L56.0004 10.6131L59.9088 11.8364L60.4305 10.1272L54.7603 8.34866L51.8697 17.8055L57.6681 19.6274L58.1898 17.9183L54.1531 16.6516L54.9057 14.1963Z" fill="#FF9A31"/>
                  <path d="M69.2735 23.8266C69.2735 23.8266 69.2992 23.8266 69.3077 23.8266C69.7268 23.8179 70.1202 23.6357 70.4195 23.3234C70.7188 23.011 70.8813 22.5946 70.8728 22.1695C70.8642 21.7443 70.6846 21.3366 70.3767 21.0416C69.7353 20.4169 68.7433 20.4343 68.1275 21.085C67.8282 21.3973 67.6657 21.8138 67.6742 22.2389C67.6828 22.664 67.8624 23.0631 68.1703 23.3668C68.4696 23.6617 68.863 23.8266 69.2735 23.8266Z" fill="#FF9A31"/>
                  <path d="M77.4665 41.1091C77.5435 41.1091 77.6205 41.1091 77.706 41.1091H77.8942L77.7744 39.2958H77.6119C76.312 39.2091 75.6706 38.7492 75.3285 37.6561C74.8239 36.0684 75.6877 34.6976 77.5777 34.0816C79.4678 33.4569 80.9559 34.0555 81.469 35.6432C81.8196 36.7364 81.5631 37.4825 80.5539 38.3241L80.4342 38.4282L81.3835 39.9639L81.5374 39.8598C83.1794 38.7666 83.7695 37.0314 83.1452 35.0966C82.7689 33.9167 81.9992 32.9797 80.973 32.4505C79.8355 31.8605 78.4672 31.8085 77.0047 32.2856C75.5423 32.7628 74.4647 33.6304 73.8832 34.7756C73.3615 35.8168 73.2759 37.0401 73.6522 38.2113C74.2423 40.068 75.6278 41.1178 77.4751 41.1178L77.4665 41.1091Z" fill="#FF9A31"/>
                  <path d="M83.7951 46.2453C82.9656 45.2996 81.6999 44.7443 80.1519 44.6402C78.6039 44.5361 77.2869 44.9265 76.3376 45.7594C75.4824 46.5142 74.9607 47.6161 74.8837 48.8654C74.8067 50.1061 75.1745 51.2687 75.9356 52.1276C76.7652 53.0733 78.0309 53.6285 79.5789 53.7326C79.7414 53.7413 79.9039 53.75 80.0578 53.75C83.1708 53.75 84.7102 51.6678 84.8471 49.5075C84.924 48.2668 84.5563 47.1042 83.7951 46.2453ZM83.0853 49.3947C83.0426 50.1408 82.7347 50.7655 82.1959 51.1993C81.5972 51.6851 80.7334 51.902 79.6986 51.8326C77.3297 51.6764 76.5685 50.2536 76.6455 48.9695C76.6882 48.2234 76.9961 47.5987 77.5349 47.1649C78.0651 46.7398 78.7921 46.5142 79.673 46.5142C79.7927 46.5142 79.9124 46.5142 80.0322 46.5229C81.067 46.5923 81.8965 46.922 82.4268 47.4773C82.9057 47.9805 83.1281 48.6399 83.0853 49.386V49.3947Z" fill="#FF9A31"/>
                  <path d="M71.4201 61.7492L73.045 62.4433L74.6186 58.6606L81.9907 61.81L82.7005 60.0921L73.7121 56.24L71.4201 61.7492Z" fill="#FF9A31"/>
                  <path d="M65.998 68.8028L67.3578 69.9307L69.9235 66.7466L76.1153 71.8915L77.2869 70.4426L69.7353 64.1698L65.998 68.8028Z" fill="#FF9A31"/>
                  <path d="M58.5746 73.8956L59.5153 75.4052L63.0902 73.1234L64.4414 75.2924L61.6448 77.0797L62.5856 78.5893L65.3822 76.8107L66.417 78.4765L62.9533 80.6889L63.8855 82.1985L68.9142 78.9884L63.7145 70.6161L58.5746 73.8956Z" fill="#FF9A31"/>
                  <path d="M51.8611 75.8911C49.9026 76.3336 48.7224 77.7304 48.6369 79.7259V79.9167L50.4158 79.9601L50.4414 79.7953C50.6381 78.4939 51.1512 77.8866 52.2545 77.635C53.8538 77.2706 55.1195 78.2683 55.5556 80.2378C55.9918 82.2072 55.2649 83.6561 53.6656 84.0205C52.5624 84.2721 51.8525 83.9424 51.117 82.8492L51.023 82.7104L49.4237 83.5259L49.5092 83.6908C50.2533 85.1223 51.4848 85.8858 52.9643 85.8858C53.3064 85.8858 53.6656 85.8424 54.0419 85.7643C57.0779 85.0703 57.899 82.1985 57.3687 79.8213C56.847 77.4441 54.8971 75.197 51.8525 75.8911H51.8611Z" fill="#FF9A31"/>
                  <path d="M43.386 76.5591L41.5558 76.3509L40.6493 84.4022L38.0323 84.1072L37.8356 85.8771L44.8912 86.6927L45.0879 84.9228L42.4794 84.6191L43.386 76.5591Z" fill="#FF9A31"/>
                  <path d="M34.5733 74.3905L31.1115 83.6452L32.8298 84.3066L36.2915 75.0519L34.5733 74.3905Z" fill="#FF9A31"/>
                  <path d="M19.585 76.5765L21.1843 77.7477L27.0426 73.3837L24.8019 80.3939L26.4012 81.5652L29.4115 71.5618L28.0004 70.5293L19.585 76.5765Z" fill="#FF9A31"/>
                  <path d="M20.1922 61.7059L18.7725 62.7643L21.2612 66.2261L19.2173 67.7357L17.2759 65.0374L15.8477 66.0872L17.7976 68.7941L16.224 69.9567L13.8122 66.5991L12.3926 67.6489L15.8904 72.5161L23.7756 66.6772L20.1922 61.7059Z" fill="#FF9A31"/>
                  <path d="M12.6663 54.1925C13.0939 54.1231 13.4617 53.8888 13.7097 53.5418C13.9577 53.1947 14.0518 52.7696 13.9833 52.3358C13.9149 51.902 13.684 51.529 13.3419 51.2774C12.9998 51.0257 12.5808 50.9303 12.1532 50.9997C11.2723 51.1472 10.6993 51.9628 10.8447 52.8564C10.9131 53.2902 11.144 53.6632 11.4861 53.9148C11.7512 54.1057 12.0762 54.2098 12.4012 54.2098C12.4953 54.2098 12.5808 54.2098 12.6749 54.1838L12.6663 54.1925Z" fill="#FF9A31"/>
                  <path d="M45.8575 74.9627C35.9198 74.9627 26.307 69.5056 21.3724 59.9794C14.2997 46.3147 19.5166 29.3532 32.9863 22.1868C46.4647 15.0118 63.1757 20.3041 70.2399 33.9688C73.6608 40.5886 74.345 48.1714 72.1471 55.303C69.9491 62.4433 65.1513 68.2909 58.626 71.7613C54.5465 73.9303 50.1678 74.9627 45.8489 74.9627H45.8575ZM45.7805 20.4516C41.607 20.4516 37.4677 21.4754 33.6705 23.4969C20.9106 30.2902 15.9759 46.3494 22.6723 59.2939C27.3419 68.317 36.45 73.4792 45.8575 73.4792C49.9454 73.4792 54.0933 72.5075 57.9589 70.4512C64.1422 67.1631 68.6919 61.6278 70.7616 54.8692C72.8398 48.1106 72.1984 40.9356 68.9571 34.6629C65.7158 28.3901 60.2594 23.7745 53.5972 21.6749C51.0316 20.8594 48.406 20.4603 45.789 20.4603L45.7805 20.4516Z" fill="#FF9A31"/>
                  <path d="M45.8402 93.0001C29.5054 93.0001 13.7094 84.0379 5.61044 68.3777C-6.01206 45.9156 2.54873 18.0484 24.6905 6.25778C46.8322 -5.53286 74.302 3.16046 85.916 25.6225C97.5385 48.0846 88.9691 75.9519 66.8274 87.7338C60.1224 91.2997 52.93 92.9915 45.8316 92.9915L45.8402 93.0001ZM45.6948 4.71346C39.1694 4.71346 32.5585 6.26646 26.3924 9.55465C6.03805 20.3823 -1.83857 46.0024 8.84318 66.6512C19.5249 87.3 44.7797 95.2906 65.134 84.4543C85.4884 73.618 93.365 47.9979 82.6832 27.3491C75.2342 12.9556 60.704 4.71346 45.6948 4.71346Z" fill="#FF9A31"/>
                  <path d="M60.3193 37.6821C58.8568 35.8601 56.4109 29.7436 54.9143 29.0842C53.4176 28.4335 51.3223 25.3448 51.3223 25.3448C51.3223 25.3448 48.4573 28.0604 47.4653 28.9714C46.4732 29.8737 41.4787 32.4591 39.1183 33.2313C36.7578 34.0035 37.4933 35.8688 34.0126 37.0227C30.5318 38.1766 27.8208 41.2653 27.8208 41.2653C27.778 41.3173 27.7523 41.378 27.7267 41.4388L26.555 39.6602C26.9142 39.2351 27.0511 38.7666 26.8544 38.4629C26.5721 38.0291 25.7255 38.0812 24.9643 38.5844C24.2032 39.0876 23.8098 39.8511 24.092 40.2935C24.3058 40.6232 24.8531 40.6753 25.4347 40.4497L45.7463 71.4403C45.8831 71.6485 46.114 71.7613 46.3535 71.7439C46.4732 71.7439 46.5844 71.7006 46.6956 71.6312C47.012 71.4229 47.089 70.9891 46.8837 70.6768L45.4127 68.4384C46.3107 67.9005 47.012 64.1438 48.654 63.4844C51.4164 62.3652 56.5392 55.2336 56.5392 55.2336C56.5392 55.2336 52.5196 57.177 49.4152 58.2181C51.9552 55.7281 54.6662 51.9541 54.6662 51.9541C54.6662 51.9541 47.9356 55.2075 45.1733 55.5459C43.1891 55.7888 41.8208 57.0642 41.205 57.767C40.9228 57.0816 40.5379 55.9797 40.2472 55.3117C40.9057 55.4591 45.1561 53.1687 46.4817 52.2924C48.004 51.286 48.8251 50.1408 51.1513 48.8394C53.4775 47.538 56.5905 46.0544 57.651 44.5101C58.7114 42.9657 64.0994 40.4324 64.0994 40.4324C64.0994 40.4324 61.7475 39.5127 60.2936 37.6908L60.3193 37.6821ZM45.1904 59.4327L44.8568 57.4893L46.3107 58.7994L48.0297 57.8711L47.2514 59.6757L48.654 61.0465L46.7212 60.8469L45.866 62.6255L45.4469 60.6994L43.5227 60.4218L45.1989 59.4241L45.1904 59.4327ZM42.6931 64.2566L28.351 42.3671C29.9332 43.7119 34.8421 48.475 36.3046 50.8435C38.0321 53.6459 40.9912 59.7798 42.6931 64.2566ZM52.9814 42.5493C52.7249 43.4863 52.2802 44.3713 51.6473 45.0914C47.4653 49.8545 39.7682 47.3037 39.4689 40.4844C39.4261 39.4954 39.64 38.4976 40.0932 37.6213C41.7524 34.4199 44.9937 33.0578 48.004 33.7952L47.7218 34.9752C45.1561 34.3505 42.3938 35.5651 41.0596 38.3935C40.786 38.9661 40.6491 39.6081 40.6577 40.2415C40.709 45.2996 45.6094 47.6508 49.2014 45.5772C50.3474 44.9178 51.2111 43.9028 51.6815 42.6708L50.8092 42.4278C50.0566 44.2671 48.2435 45.5252 45.9258 45.4644C45.3614 45.4471 44.797 45.2909 44.3009 44.9959C40.5807 42.8183 40.9827 38.1246 44.0358 36.3547C45.0963 35.7473 46.3278 35.5651 47.508 35.8514L47.2258 37.0314C45.6351 36.641 43.9161 37.3784 43.0609 39.1049C42.8556 39.5127 42.753 39.9812 42.7701 40.4324C42.8641 44.1283 47.1061 45.4818 49.2612 42.7488C49.4836 42.4712 49.723 41.942 49.8171 41.5949L49.9967 40.9356L53.161 41.8205L52.9643 42.5406L52.9814 42.5493Z" fill="#FF9A31"/>
                </g>
              </svg>
        </>
    );
};

export default FooterRightImage;