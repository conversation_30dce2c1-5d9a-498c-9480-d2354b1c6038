import React, { useState } from 'react';
import Link from 'next/link';
import { footerNavItems } from '@/layout/Footer/data';
import { successModalDto } from '@/Interface/Dto';
import { useSession } from 'next-auth/react';
import SuccessfulModal from '@/components/Modal/SuccessfulModal';
import { playfair_display } from '@/utils/fonts';
import FooterLogo from '@/assets/svg/footer-logo-svg.svg';
import { useSelector } from 'react-redux';
import { RootState } from '@/libs/store/store';

export interface FooterOptions {
  socialBarIsWhite: boolean;
  noFooter: boolean;
}

export interface IFooterProps {
  options?: Partial<FooterOptions>;
}

const Footer: React.FC<IFooterProps> = (props) => {
  const [showSuccessModal, setShowSuccessModal] = useState<successModalDto>({});
  const currentYear = new Date().getFullYear();

  const session = useSelector((state: RootState) => state.user);

  const { options = {} } = props;

  if (options?.noFooter) return <></>;
  return (
    <>
      <footer
        className="container mx-auto bg-orange-texture px-4 md:px-10"
        id="footerId"
      >
        <div className="flex flex-col lg:flex-row gap-12 justify-between py-10 md:py-[80px]">
          <div className="flex flex-col lg:flex-row gap-12 justify-start">
            <div className="w-48">
              <div className="text-lg font-medium leading-[19.8px] uppercase text-black mb-5">
                OTHER LINKS
              </div>
              <ul>
                {footerNavItems.map((navItem, navItemIdx) => {
                  if (navItem.id === 'fi-account-3') {
                    if (session?.user?.loggedIn) {
                      // Show "Account settings" if the user is logged in
                      return (
                        <li key={`_fni_${navItem.id}-${navItemIdx}`}>
                          <Link
                            href={navItem.href}
                            rel="nofollow"
                            className={`transition-all text-lg ease-out duration-300 font-normal text-black leading-[40px] underline hover:underline ${playfair_display}`}
                          >
                            {navItem.name}
                          </Link>
                        </li>
                      );
                    } else {
                      // Don't show "Account settings" if the user is not logged in
                      return null;
                    }
                  } else {
                    // For other items in the list, always render them
                    return (
                      <li key={`_fni_${navItem.id}-${navItemIdx}`}>
                        <Link
                          href={navItem.href}
                          rel="nofollow"
                          className={`transition-all ease-out text-lg duration-300 font-normal text-black leading-[40px] underline hover:underline ${playfair_display}`}
                        >
                          {navItem.name}
                        </Link>
                      </li>
                    );
                  }
                })}
              </ul>
            </div>

            <div className="w-52">
              <div className="text-lg font-medium leading-[19.8px] uppercase text-black mb-5">
                FOLLOW US
              </div>
              <div className="flex flex-col">
                <Link
                  href="https://www.instagram.com/cruisecollectiveuk/"
                  target="_blank"
                  className={`transition-all ease-out duration-300 font-normal text-black leading-[40px] underline hover:underline text-lg ${playfair_display}`}
                >
                  On Instagram
                </Link>
                <Link
                  href="https://www.facebook.com/profile.php?id=61554439305290"
                  target="_blank"
                  className={`transition-all ease-out duration-300 font-normal text-black leading-[40px] underline hover:underline text-lg ${playfair_display}`}
                >
                  On Facebook
                </Link>
                <Link
                  href="https://x.com/Cruis_Collectiv"
                  target="_blank"
                  className={`transition-all ease-out duration-300 font-normal text-black leading-[40px] underline hover:underline text-lg ${playfair_display}`}
                >
                  On X
                </Link>
              </div>
            </div>
          </div>

          <div className="flex-auto lg:ml-auto lg:order-last">
            <div className="flex justify-start lg:justify-end">
              <img
                className="w-[180px] md:w-[284px]"
                src="/images/footer-logo.png"
                alt="footer-logo"
              />
            </div>
          </div>
        </div>

        <div
          className={`copyRightFooterContainer pb-6 text-sm text-black w-full ${playfair_display} font-normal`}
        >
          Copyright © {currentYear} Cruise Collective. All rights reserved.
        </div>
      </footer>
      {!!Object.keys(showSuccessModal).length && (
        <SuccessfulModal
          showSuccessModal={showSuccessModal}
          setShowSuccessModal={setShowSuccessModal}
        />
      )}
    </>
  );
};

export default Footer;
