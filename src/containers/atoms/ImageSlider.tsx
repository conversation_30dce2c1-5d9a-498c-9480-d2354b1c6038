import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/navigation';
import { Autoplay, Navigation } from 'swiper';
import Link from 'next/link';
import Image from 'next/image';
import 'swiper/css/bundle';

// Import your custom arrow images as regular images
import customPrevArrow from '/public/images/BannerAssete.png';
import customNextArrow from '/public/images/BannerAssetw.png';
import { playfair_display } from '@/utils/fonts';
import { ParallaxBanner } from 'react-scroll-parallax';

const ImageSliderItem = (props) => {
  const { title, description, image, video, permalink } = props;

  const renderMedia = () => {
    if (video?.url) {
      return (
        <iframe
          className="video-iframe"
          src={video?.url}
          frameBorder="0"
          allow="autoplay; fullscreen; muted"
          allowFullScreen
        ></iframe>
      );
    } else if (image?.data?.attributes?.url) {
      return (
        <ParallaxBanner
          className="h-full"
          style={{ aspectRatio: '2 / 1' }}
          layers={[
            {
              image: image?.data?.attributes?.url,
              speed: -20,
            },
            {
              // Adding the gradient overlay layer
              children: (
                <div
                  className="absolute inset-0"
                  style={{
                    background:
                      'linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.4) 100%)',
                  }}
                />
              ),
            },
          ]}
        />

        // <Image
        //   src={image?.data?.attributes?.url}
        //   alt={image?.data?.attributes?.name || ''}
        //   fill
        //   style={{ objectFit: 'cover', objectPosition: 'center' }}
        //   priority={true}
        // />
      );
    }

    return null;
  };

  return (
    <div className="h-screen w-full relative">
      <div className="absolute inset-0 bg-black opacity-30 z-10"></div>

      {renderMedia()}
      {video?.url ? null : (
        <div className="fixed-logo w-full md:w-3/4 lg:w-[1010px]">
          <div className="flex flex-col text-white items-center text-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="202"
              height="228"
              viewBox="0 0 202 228"
              fill="none"
            >
              <path
                d="M114.26 227.989C51.2573 227.989 0 176.848 0 114C0 51.1516 51.2573 0 114.26 0C144.783 0 173.468 11.8605 195.056 33.3882L181.134 47.2781C163.275 29.4602 139.522 19.6401 114.26 19.6401C62.1168 19.6401 19.6849 61.9646 19.6849 114C19.6849 166.035 62.1059 208.36 114.26 208.36C136.209 208.36 157.02 200.951 173.818 187.312L163.472 176.99C149.474 187.923 132.326 193.848 114.26 193.848C70.133 193.848 34.2299 158.027 34.2299 114C34.2299 69.9734 70.133 34.152 114.26 34.152C135.64 34.152 155.73 42.4554 170.843 57.5346L156.922 71.4246C145.526 60.0551 130.369 53.7921 114.249 53.7921C80.9815 53.7921 53.9038 80.7973 53.9038 114C53.9038 147.203 80.9706 174.208 114.249 174.208C130.369 174.208 145.515 167.945 156.922 156.575L163.877 149.636L202 187.672L195.045 194.612C173.468 216.14 144.772 228 114.249 228L114.26 227.989Z"
                fill="#FF9A31"
              />
            </svg>

            <div
              className={`text-[48px] md:text-[60px] font-normal text-white text-left md:text-center leading-[52.8px] md:leading-[66px] ${playfair_display} my-4 md:my-10 px-4 md:px-10`}
            >
              {title}
            </div>
            <p className="text-lg md:text-2xl font-medium leading-[19.8px] md:leading-[26.4px] uppercase mt-4 text-white text-left md:text-center w-full px-4 md:px-10">
              {description}
            </p>
          </div>
        </div>
      )}
      {/* {(title || description || permalink) && !video?.url && <></>} */}
    </div>
  );
};

const ImageSlider = (props) => {
  const { sliderItems, video } = props;

  return (
    <div className="w-full relative">
      <Swiper
        navigation={{
          prevEl: '.custom-swiper-button-prev',
          nextEl: '.custom-swiper-button-next',
        }}
        modules={[Navigation, Autoplay]}
        autoplay={{ delay: 7000 }}
      >
        {sliderItems?.map((sliderItem, sliderItemIdx) => (
          <SwiperSlide key={`slider-${sliderItem.id}-${sliderItemIdx}`}>
            <ImageSliderItem {...sliderItem} />
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom arrow elements */}
      <div className=" custom-swiper-button-prev">
        <img className="slider-img" src={customPrevArrow.src} alt="Prev" />
      </div>
      <div className=" custom-swiper-button-next">
        <img className="slider-img" src={customNextArrow.src} alt="Next" />
      </div>
    </div>
  );
};
export default ImageSlider;
