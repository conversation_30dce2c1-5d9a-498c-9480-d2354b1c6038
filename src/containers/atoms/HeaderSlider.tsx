import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/navigation';
import { Navigation } from 'swiper';
import customPrevArrow from '/public/images/BannerAssete.png';
import customNextArrow from '/public/images/BannerAssetw.png';
import HeaderImageSliderItem from './HeaderImageSliderItem';
import RightIcon from '@/assets/svg/right-icon.svg';
import { playfair_display } from '@/utils/fonts';

const HeaderSlider = (props) => {
  const { sliderItems, source, video } = props;

  return (
    <div className="w-full h-full">
      <Swiper
        // navigation={{
        //   prevEl: '.custom-swiper-button-prev',
        //   nextEl: '.custom-swiper-button-next',
        // }}
        modules={[Navigation]}
      >
        {sliderItems?.map((sliderItem, sliderItemIdx) => (
          <SwiperSlide key={`slider-${sliderItem.id}-${sliderItemIdx}`}>
            <HeaderImageSliderItem {...sliderItem} video={video} />
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom arrow elements */}
      {/* <div className=" custom-swiper-button-prev hidden md:block">
        <img className="slider-img" src={customPrevArrow.src} alt="Prev" />
      </div>
      <div className=" custom-swiper-button-next hidden md:block">
        <img className="slider-img" src={customNextArrow.src} alt="Next" />
      </div> */}
    </div>
  );
};

export default HeaderSlider;
