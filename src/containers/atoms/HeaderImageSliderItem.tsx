import React, { useEffect, useRef, useState } from 'react';
import Player from '@vimeo/player';

const HeaderImageSliderItem = ({ attributes, video }) => {
  const [videoError, setVideoError] = useState(false);
  const iframeRef = useRef(null);

  useEffect(() => {
    if (video?.provider === 'vimeo' && iframeRef.current) {
      const player = new Player(iframeRef.current);

      player.on('play', () => {
        console.log('Vimeo video is playing');
      });

      player.on('error', (error) => {
        console.error('Vimeo video error:', error);
        setVideoError(true);
      });

      return () => {
        player.off('play');
        player.off('error');
      };
    }
  }, [video]);

  const videoUrl =
    video?.provider === 'vimeo'
      ? `https://player.vimeo.com/video/${video.providerUid}?autoplay=1&loop=1&muted=1&background=1`
      : video?.url;

  return videoUrl && !videoError ? (
    <div className="relative" style={{ padding: '56.25% 0 0 0' }}>
      <iframe
        className={`absolute top-0 left-0 w-full h-full transition-opacity duration-500 ${
          video?.provider === 'vimeo' ? 'px-0' : 'px-10'
        }`}
        src={videoUrl}
        allow="autoplay; fullscreen"
        allowFullScreen
        style={{
          pointerEvents: 'none',
          border: 'none',
        }}
        loading="lazy"
        ref={iframeRef} // Attach the ref to the iframe
      ></iframe>
    </div>
  ) : attributes?.url ? (
    <div
      className="bg-cover bg-center bg-no-repeat w-full h-full"
      style={{
        backgroundImage: `linear-gradient(0deg, rgba(0, 0, 0, 0.20) 0%, rgba(0, 0, 0, 0.20) 100%), url('${attributes?.url}')`,
      }}
    ></div>
  ) : null;
};

export default HeaderImageSliderItem;
