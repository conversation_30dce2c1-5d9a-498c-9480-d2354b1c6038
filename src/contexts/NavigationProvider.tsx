import {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from 'react';
import { baseUrl } from '@/utils';

interface NavbarItemProps {
  id: number;
  label: string;
  href: string;
  matcher?: RegExp;
  sub?: NavbarItemProps[];
}

const NavigationContext = createContext<NavbarItemProps[] | null>(null);

interface NavigationProviderProps {
  children: ReactNode;
}

let cachedNavigation: NavbarItemProps[] | null = null;

// 🌟 `staticMenuItems` formatına uygun veri dönüşümü
const mapNavigationItems = (items: any[]): NavbarItemProps[] => {
  const mappedItems: { [key: number]: NavbarItemProps } = {};
  const rootItems: NavbarItemProps[] = [];

  items.forEach((item) => {
    // 🛠️ Eğer externalPath yoksa, parent & related kullanılarak slug oluşturuluyor
    let computedHref = item.externalPath || (item.path ? `/${item.path}` : '#');

    if (!item.externalPath) {
      if (item.related?.slug && item.parent?.path) {
        computedHref = `/${item.parent.path}/${item.related.slug}`;
      } else if (item.related?.slug) {
        computedHref = `/${item.related.slug}`;
      } else if (item.parent?.path) {
        computedHref = `/${item.parent.path}`;
      }
    }

    mappedItems[item.id] = {
      id: item.id,
      label: item.title,
      href: computedHref,
      matcher: item.path ? new RegExp(`^/${item.path}/(.*)`) : undefined,
      sub: [],
    };
  });

  items.forEach((item) => {
    if (item.parent && mappedItems[item.parent.id]) {
      mappedItems[item.parent.id].sub?.push(mappedItems[item.id]);
    } else {
      rootItems.push(mappedItems[item.id]);
    }
  });

  return rootItems;
};

export const NavigationProvider: React.FC<NavigationProviderProps> = ({
  children,
}) => {
  const [navigation, setNavigation] = useState<NavbarItemProps[] | null>(
    cachedNavigation,
  );

  useEffect(() => {
    if (cachedNavigation) return;

    const fetchNavigation = async () => {
      try {
        const res = await fetch(
          `${baseUrl}/api/navigation/render/main-navigation`,
        );
        const data = await res.json();
        const structuredNavigation = mapNavigationItems(data);
        cachedNavigation = structuredNavigation;
        setNavigation(structuredNavigation);
      } catch (error) {
        console.error('Navigation fetch error:', error);
      }
    };

    fetchNavigation();
  }, []);

  return (
    <NavigationContext.Provider value={navigation}>
      {children}
    </NavigationContext.Provider>
  );
};

export const useNavigation = () => useContext(NavigationContext);
