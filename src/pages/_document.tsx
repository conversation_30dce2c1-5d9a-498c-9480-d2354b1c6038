import { Html, <PERSON>, Main, NextScript } from 'next/document';
import { GoogleAnalytics } from '@next/third-parties/google';
import { poppins } from '@/utils/fonts';

export default function Document() {
  return (
    <Html
      lang="en"
      data-theme="light"
      style={{ scrollBehavior: 'smooth', overflowX: 'hidden' }}
    >
      <Head>
        {/* Favicon */}
        <link rel="shortcut icon" href="/favicon/favicon.ico" />

        {/* Adobe Fonts */}
        <link rel="stylesheet" href="https://use.typekit.net/zzu0znm.css" />

        {/* Vimeo Preconnect and Script */}
        <link rel="preconnect" href="https://player.vimeo.com" />
        <script async src="https://player.vimeo.com/api/player.js"></script>

        {/* Google Analytics */}
        <GoogleAnalytics gaId="G-4CKLFM137G" />

        {/* Hotjar Tracking Script */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function(h,o,t,j,a,r){
                  h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                  h._hjSettings={hjid:3870020,hjsv:6};
                  a=o.getElementsByTagName('head')[0];
                  r=o.createElement('script');r.async=1;
                  r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                  a.appendChild(r);
              })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
            `,
          }}
        />

        {/* Riddle Embed Script */}
        <script src="https://www.riddle.com/embed/build-embedjs/embedV2.js"></script>

        {/* Facebook Pixel (NoScript Fallback) */}
        <noscript>
          <img
            height="1"
            width="1"
            style={{ display: 'none' }}
            src={`https://www.facebook.com/tr?id=${process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID}&ev=PageView&noscript=1`}
          />
        </noscript>
      </Head>
      <body className={`${poppins}`}>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
