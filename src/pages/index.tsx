import Head from 'next/head';
import HomeLandingPage from '@/components/LandingPage/HomeLandingPage';

export default function Home({ title, description }) {
  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="description" content={description} />
      </Head>
      <main>
        <HomeLandingPage />
      </main>
    </>
  );
}

export const getStaticProps = async () => {
  return {
    props: {
      title: 'Cruise Collective',
      description: 'Find adventure, luxury and exclusive savings with Cruise Collective; where members gain access to discounts, incredible competition prizes and insider knowledge',
    },
    revalidate: 2,
  };
};
