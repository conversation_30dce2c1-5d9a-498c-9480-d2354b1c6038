import PageHeader from '@/components/Shared/PageHeader';
import { baseUrl } from '@/utils';
import styles from '../../styles/article.module.css';
import { playfair_display } from '@/utils/fonts';

const TermsANdConditionSingle = ({ data }) => {
  const pageData = {
    subTitle: 'CRUISE COLLECTIVE',
    title: data?.title,
  };


  return (
    <>
      <div className="container mx-auto px-4 md:px-10">
        <PageHeader data={pageData} />
      </div>
      <div
        className={`container mx-auto px-4 md:px-10 ${styles.articleContainer} ${playfair_display}`}
        dangerouslySetInnerHTML={{
          __html: data?.attributes?.description,
        }}
      ></div>
    </>
  );
};

export async function getServerSideProps(context) {
  const { params } = context;
  const slug = params.slug;
  const apiUrl = `${baseUrl}/api/terms-and-conditions?populate=deep&filters[slug][$eq]=${slug}`;
  const res = await fetch(apiUrl);
  const { data } = await res.json();

  return {
    props: {
      data: data?.length ? data[0] : null,
    },
  };
}

export default TermsANdConditionSingle;
