import { ctaInfoBlocks, ICompetitionOptions } from '@/Interface/Dto';
import ArticleCard from '@/components/Card/ArticleCard';
import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import Loader from '@/components/Loader';
import PageHeading from '@/components/PageHeading';
import Seo from '@/components/Seo';
import RenderComponent from '@/helpers/RenderComponent';
import useInfiniteScroll from '@/hooks/useInfiniteScroll';
import { getCompetitions } from '@/queries/competitons';
import { useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import Select from 'react-select';

const options = [
  { value: true, label: 'Open' },
  { value: false, label: 'Closed' },
];

const Competition = () => {
  const { cards, hasMore, fetchMoreData } = useInfiniteScroll(getCompetitions);

  const metaInfo = {
    metaTitle: 'Enter Exciting Cruise Competitions & Win',
    metaDescription:
      "Join Cruise Collective's exclusive competitions for a chance to win amazing experiences from our partners. Participate now for your chance to win great prizes.",
  };

  return (
    <>
      <Seo data={metaInfo} />
      <div className="py-10 md:py-[80px]">
        <div className="px-4 md:px-10 container mx-auto">
          <section>
            <PageHeading
              pageHeaderData={{
                heading: 'Member competitions',
                text: '',
              }}
            />

            <div className="pt-1 max-w-4xl justify-normal text-black mt-6">
              <p className="text-lg">
                Each month we feature competitions from our exclusive cruise
                partners… Dive into Cruise Collective Competitions and be in
                with a chance to win some incredible prizes. At Cruise
                Collective, we love giving you the chance to win fantastic
                prizes, from dreamy short getaways to unique expedition cruise
                experiences. Test your luck and knowledge by entering today.
                Whether you&apos;re after a tropical escape or a cultural
                adventure, there&apos;s something for everyone, read on to find
                out more…
              </p>
            </div>
          </section>

          <section>
            <InfiniteScroll
              dataLength={cards?.length}
              next={fetchMoreData}
              hasMore={hasMore}
              loader={<Loader />}
            >
              <div className="container mx-auto my-10 space-y-12">
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                    {cards?.slice(0, 2).map((article, index) => (
                      <ArticleCard
                        key={article.id}
                        article={article}
                        page="competition"
                        className={`${index === 0 ? 'lg:col-span-2' : ''}`}
                      />
                    ))}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    {cards?.slice(2, 4).map((article, index) => (
                      <ArticleCard
                        key={article.id}
                        article={article}
                        page="competition"
                      />
                    ))}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {cards?.slice(4).map((article) => (
                      <ArticleCard
                        key={article.id}
                        article={article}
                        page="competition"
                      />
                    ))}
                  </div>
                </>
              </div>
            </InfiniteScroll>
          </section>
        </div>
        <div className="my-12 md:px-10">
          <RenderComponent
            blocks={ctaInfoBlocks}
            componentType="blocks.call-to-actions"
            sort={3}
          />
        </div>
      </div>
    </>
  );
};

export default Competition;
