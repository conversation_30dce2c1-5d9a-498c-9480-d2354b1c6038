import FullScreenCard from '@/components/Card/FullScreenCard';
import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import Loader from '@/components/Loader';
import { Loading } from '@/components/Loading';
import PageHeader from '@/components/Shared/PageHeader';
import useInfiniteScroll from '@/hooks/useInfiniteScroll';
import { getInterests } from '@/queries';
import InfiniteScroll from 'react-infinite-scroll-component';

const NewToCruise = () => {
  const { isLoading, cards, hasMore, fetchMoreData } =
    useInfiniteScroll(getInterests);
  const data = {
    subTitle: 'New to cruise',
    title:
      'Experience luxury and adventure with our top cruise partners worldwide',
  };

  if (isLoading) return <Loading />;

  return (
    <>
      <DefaultMetaTitle title="New To Cruise" />

      <div className="container mx-auto px-4 md:px-10">
        <PageHeader data={data} />
      </div>

      <div>
        <InfiniteScroll
          dataLength={cards?.length}
          next={fetchMoreData}
          hasMore={hasMore}
          loader={<Loader />}
        >
          <div className="my-10 grid grid-cols-1 md:grid-cols-2 gap-4">
            {cards.map((interest, index) => (
              <FullScreenCard
                key={index}
                data={interest}
                index={index}
                page="interest"
                text="CRUISE INTERESTS"
              />
            ))}
          </div>
          {/* {!cards.length && (
          <p className="text-2xl text-center">No data found!</p>
        )} */}
        </InfiniteScroll>
      </div>
    </>
  );
};

export default NewToCruise;
