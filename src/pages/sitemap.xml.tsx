function SiteMap() {
 
}

export async function getServerSideProps({ res }) {
    try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/sitemap/index.xml`, {  
          method: 'GET',
          headers: {
            'Content-Type': 'text/xml',
          },
        });
      
        if (!response.ok) {
          throw new Error(`Failed to fetch sitemap: ${response.statusText}`);
        }
      
        const sitemapTextXml = await response.text();
        const cleanedSitemapTextXml = sitemapTextXml.replace(/<\?xml-stylesheet [^>]*\?>/, '');

        res.setHeader('Content-Type', 'text/xml');
        res.write(cleanedSitemapTextXml);
        res.end();

      } catch (error) {
        console.error('Error fetching sitemap:', error);
        res.statusCode = 500;
        res.end();
      }
      

  return {
    props: {},
  };
}

export default SiteMap;