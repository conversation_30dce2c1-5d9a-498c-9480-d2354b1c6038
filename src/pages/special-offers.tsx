import FullScreenCard from '@/components/Card/FullScreenCard';
import { Discount } from '@/components/SavingBlock/Discount';
import Seo from '@/components/Seo';
import { useAppDispatch, useAppSelector } from '@/libs/hooks';
import {
  setDiscountData,
  toggleDiscountModal,
  toggleLoginModal,
} from '@/libs/store/setting';
import { setSSOData } from '@/libs/store/ssoRedirect';
import { spacialOffers } from '@/queries';
import { playfair_display } from '@/utils/fonts';
import React, { useEffect } from 'react';
import { useQuery } from 'react-query';

const SpacialOffers = () => {
  const { data } = useQuery('spacialOffers', spacialOffers, {
    refetchOnWindowFocus: false,
    enabled: true,
  });
  const user = useAppSelector((state) => state.user);
  const dispatch = useAppDispatch();
  const setting = useAppSelector((state) => state.setting);
  const ssoRedirect = useAppSelector((state) => state.ssoRedirect);
  const bgColor = data?.bg_color;
  const bgImage = data?.backgroundImage?.data?.attributes?.url;
  const iconImage = data?.icon_image?.data?.attributes?.url;
  const stumpItemImage = data?.stump_item_image?.data?.attributes?.url;

  const handleDiscountClick = (discountData) => {
    if (!discountData?.attributes?.calloutbox) return;

    dispatch(setDiscountData(discountData?.attributes));

    if (!user.loggedIn) {
      dispatch(toggleLoginModal());
    } else {
      dispatch(
        toggleDiscountModal({
          isModalOpen: true,
          data: discountData?.attributes,
        }),
      );
    }
  };

  useEffect(() => {
    if (user.loggedIn) {
      const modalData = setting?.discountModal?.data || ssoRedirect?.data;

      if (modalData?.calloutbox) {
        dispatch(
          toggleDiscountModal({
            isModalOpen: true,
            data: modalData,
          }),
        );
        dispatch(setSSOData({ page: '/', data: {} }));
      }
    }
  }, [user.loggedIn]);

  const metaInfo = {
    metaTitle: 'Black Friday Cruise Discounts & Special Offers',
    metaDescription:
      'Unlock Black Friday discounts on top cruises with Cruise Collective. Enjoy unbeatable offers and special deals tailored to your travel preferences and budget.',
  };

  const renderCruiseCards = (filterFn) =>
    data?.cruise_lines?.data?.filter(filterFn)?.map((cruise, index) => (
      <FullScreenCard
        key={index}
        data={cruise}
        index={index}
        page="discount"
        onClick={() => handleDiscountClick(cruise)}
        text="BLACK FRIDAY DISCOUNTS"
      >
        <div className="flex gap-1 md:gap-3">
          <Discount item={cruise} showDiscountBtn={false} />
          <div
            className="relative text-center items-center flex flex-col bg-no-repeat bg-center bg-cover p-3"
            style={{
              background: bgColor,
              backgroundImage: `url('${stumpItemImage}')`,
              width: '168px',
              height: '168px',
            }}
          >
            <div className="w-full text-sm text-brand text-center uppercase p-3">
              {data?.special_offer_type_text}
            </div>

            <p
              className="text-[20px] font-semibold relative z-20 transform text-white text-center p-2"
              style={{
                lineHeight: '1.2em', // Adjust
                top: '-4px',
              }}
            >
              {cruise?.attributes?.calloutbox?.special_discount_amount_info}
            </p>
          </div>
        </div>
      </FullScreenCard>
    ));

  return (
    <>
      <Seo data={metaInfo} />

      <section className="mb-10 px-4 md:px-10">
        <div
          className="text-white text-center py-12 px-6 relative"
          style={{
            backgroundColor: bgColor,
            backgroundImage: `url('${bgImage}')`,
          }}
        >
          <div className="relative max-w-[800px] mx-auto">
            <div
              className="bg-center bg-no-repeat w-full h-[150px] flex items-center justify-center"
              style={{
                ...(iconImage
                  ? { backgroundImage: `url('${iconImage}')` }
                  : {}),
              }}
            >
              <h1
                className={`text-3xl font-bold mb-4 md:text-5xl text-white text-center ${playfair_display}`}
              >
                {data?.title}
              </h1>
            </div>
            <p className={`text-base mb-6 mt-10 ${playfair_display}`}>
              {data?.excerpt}
            </p>
          </div>
        </div>
      </section>

      <section className="md:px-10">
        <div className="my-10 grid grid-cols-1 lg:grid-cols-2 gap-4">
          {renderCruiseCards(
            (item) =>
              !!item?.attributes?.calloutbox &&
              item?.attributes.slug !== 'dfds',
          )}
          {renderCruiseCards(
            (item) =>
              !!item?.attributes?.calloutbox &&
              item?.attributes.slug === 'dfds',
          )}
        </div>
      </section>
    </>
  );
};

export default SpacialOffers;
