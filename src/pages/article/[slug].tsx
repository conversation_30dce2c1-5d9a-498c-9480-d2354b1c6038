import { baseUrl } from '@/utils';
import styles from '../../styles/article.module.css';
import Seo from '@/components/Seo';
import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import ArticleCard from '@/components/Card/ArticleCard';
import ArticleSlider from '@/components/ArticleSlider';
import { playfair_display } from '@/utils/fonts';
import RenderComponent from '@/helpers/RenderComponent';
import clsx from 'clsx';
import ExploreMoreBySeaBlock from '@/components/PostPageContent/ExploreMoreBySeaBlock';
import RenderArticleEditor from '@/components/Shared/RenderArticleEditor';
import AuthorBlock from '@/components/Shared/AuthorBlock';
import ArticleEditor from '@/components/Shared/ArticleEditor';

const filterBlocksByType = (blocks, type) => {
  if (!Array.isArray(blocks)) {
    return [];
  }
  return blocks.filter((item) => item.__component === type);
};

const ArticleDetails = ({ data }) => {
  const { attributes } = data;
  const createdAt = new Date(attributes?.date);
  const options: any = { day: '2-digit', month: 'long', year: 'numeric' };
  const formattedDate = new Intl.DateTimeFormat('en-US', options)?.format(
    createdAt,
  );
  const uppercaseFormattedDate = formattedDate.toUpperCase();

  const sliderData = {
    sliders: attributes?.featured_image?.data || [],
    heading: attributes?.title,
    excerpt: attributes?.excerpt,
    date: uppercaseFormattedDate, // Use the uppercase formatted date here
    btnText: 'VIEW MORE',
    video: attributes?.video,
    partnership: attributes?.partnership,
  };

  // Filtering specific block types using the reusable function
  const splitImages = filterBlocksByType(
    attributes?.blocks,
    'blocks.split-image',
  );
  const gallery = filterBlocksByType(attributes?.blocks, 'blocks.gallery');
  const annotatedImage = filterBlocksByType(
    attributes?.blocks,
    'blocks.annotated-blocks',
  );

  const articleCta = filterBlocksByType(
    attributes?.blocks,
    'blocks.article-cta',
  );

  return (
    <>
      {data?.attributes?.seo ? (
        <Seo data={data?.attributes?.seo}></Seo>
      ) : (
        <DefaultMetaTitle title={data?.attributes?.title} />
      )}

      <ArticleSlider data={sliderData} bannerText="Articles">
        {' '}
      </ArticleSlider>

      <div className="article-category w-full md:w-3/4 mx-auto md:mt-2 hidden md:block px-2 md:px-[80px]">
        <div className="flex gap-3">
          {attributes?.related_article_categories?.data?.map((item) => (
            <a
              href={`/article?category=${item.attributes.slug}`}
              className={clsx(
                'border text-xs font-medium uppercase border-[#DCDAD6] px-3 py-1 rounded-full cursor-pointer hover:border hover:border-brand',
                {
                  'border-cruise':
                    attributes?.inspiration_category?.data?.attributes?.slug ===
                    item.attributes.slug,
                },
              )}
            >
              # {item.attributes.name}
            </a>
          ))}
        </div>
      </div>

      {attributes?.author_info && <AuthorBlock attributes={attributes} />}

      {attributes?.description && (
        <ArticleEditor
          editorText={attributes?.description}
          gallery={gallery}
          annotatedImage={annotatedImage}
          splitImages={splitImages}
          articleCta={articleCta}
        />
      )}

      <div className="my-12">
        <RenderComponent
          blocks={attributes?.blocks}
          componentType="blocks.call-to-actions"
          sort={2}
        />
      </div>

      {!!attributes?.related_articles?.articles?.data?.length && (
        <section className="container mx-auto py-10 md:py-[80px] px-4 md:px-10">
          <div
            className={`text-2xl md:text-[34px] text-black pb-4 md:pb-10 font-normal text-center ${playfair_display}`}
          >
            Related articles from the Collective
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-center">
            {attributes?.related_articles?.articles?.data?.map((item) => (
              <ArticleCard key={item.id} article={item} page="article" />
            ))}
          </div>
        </section>
      )}

      {attributes?.explore_more && (
        <section className="md:pt-[80px] md:px-10">
          <div
            className={`text-2xl md:text-[34px] text-black font-normal pb-4 md:pb-10 text-center ${playfair_display}`}
          >
            Explore more by sea
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <ExploreMoreBySeaBlock
              data={[attributes?.explore_more]}
              page="article"
            />
          </div>
        </section>
      )}

      <div className="my-10">
        <RenderComponent
          blocks={attributes?.blocks}
          componentType="blocks.call-to-actions"
          sort={3}
        />
      </div>
    </>
  );
};

export async function getServerSideProps(context) {
  const { params, query } = context;
  const slug = params.slug;
  const isPreview = query?.preview;

  const apiUrl = `${baseUrl}/api/insiprations?populate=deep&filters[slug][$eq]=${slug}${
    isPreview ? '&publicationState=preview' : ''
  }`;
  const res = await fetch(apiUrl);

  const { data } = await res.json();

  return {
    props: {
      data: data?.length ? data[0] : null,
    },
  };
}

export default ArticleDetails;
