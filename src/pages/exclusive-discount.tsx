import ArticleCard from '@/components/Card/ArticleCard';
import FullScreenCard from '@/components/Card/FullScreenCard';
import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import Loader from '@/components/Loader';
import { PromoItem } from '@/components/Promo/PromoItem';
import { Discount } from '@/components/SavingBlock/Discount';
import Seo from '@/components/Seo';
import PageHeader from '@/components/Shared/PageHeader';
import RenderComponent from '@/helpers/RenderComponent';
import useInfiniteScroll from '@/hooks/useInfiniteScroll';
import { ctaInfoDiscount } from '@/Interface/Dto';
import { useAppDispatch, useAppSelector } from '@/libs/hooks';
import {
  setDiscountData,
  toggleDiscountModal,
  toggleLoginModal,
} from '@/libs/store/setting';
import { setSSOData } from '@/libs/store/ssoRedirect';
import { getCruiseLines, getTravelPartner } from '@/queries';
import { playfair_display } from '@/utils/fonts';
import React, { useEffect, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useQuery } from 'react-query';

const ExclusiveDiscount = () => {
  const { cards, hasMore, fetchMoreData } = useInfiniteScroll(getCruiseLines);

  const user = useAppSelector((state) => state.user);
  const dispatch = useAppDispatch();
  const setting = useAppSelector((state) => state.setting);
  const ssoRedirect = useAppSelector((state) => state.ssoRedirect);

  const { data: partner } = useQuery(
    'exclusive-partner-offer',
    () => getTravelPartner(1, 6),
    {
      refetchOnWindowFocus: false,
      enabled: true,
    },
  );

  const pageData = {
    subTitle: 'Exclusive Discounts',
    title: 'Exclusive discounts on the world’s most memorable cruises',
  };

  const items = [
    {
      title: 'Unlock Exclusive Deals',
      description:
        'Enjoy member-only discounts with top cruise lines, just for being part of Cruise Collective.',
    },
    {
      title: 'Tailored Offers for You',
      description:
        'Get access to handpicked cruise deals that match your travel style and interests.',
    },
    {
      title: 'Priority Booking Perks',
      description:
        'Secure the best cabins and itineraries before anyone else with our early-access offers.',
    },
    {
      title: 'Limited-Time Savings',
      description:
        "Don't miss out on exclusive discounts and limited-time promotions from leading cruise lines.",
    },
  ];

  const onClick = (data) => {
    if (!data?.calloutbox) return;

    dispatch(setDiscountData(data));

    if (!user.loggedIn) {
      dispatch(toggleLoginModal());
      return;
    }

    dispatch(
      toggleDiscountModal({
        isModalOpen: true,
        data,
      }),
    );
  };

  useEffect(() => {
    if (user.loggedIn && setting?.discountModal?.data?.calloutbox) {
      dispatch(
        toggleDiscountModal({
          isModalOpen: true,
          data: setting?.discountModal?.data,
        }),
      );
    } else if (user.loggedIn && ssoRedirect?.data?.calloutbox) {
      dispatch(
        toggleDiscountModal({ isModalOpen: true, data: ssoRedirect?.data }),
      );
      dispatch(setSSOData({ page: '/', data: {} }));
    }
  }, [user.loggedIn]);

  const metaInfo = {
    metaTitle: 'Exclusive Cruise Discounts & Special Offers',
    metaDescription:
      'Unlock exclusive discounts on top cruises with Cruise Collective. Enjoy unbeatable offers and special deals tailored to your travel preferences and budget.',
  };

  return (
    <>
      <Seo data={metaInfo} />

      <div className="container mx-auto px-4 md:px-10">
        <PageHeader data={pageData} />
      </div>

      <div className="container mx-auto pt-10 pb-10 md:pb-[80px] px-4 md:px-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 lg:gap-[80px]">
          {items.map((item, idx) => (
            <PromoItem key={idx} item={item} idx={idx} />
          ))}
        </div>
      </div>

      <div className="md:px-10">
        <InfiniteScroll
          dataLength={cards?.length}
          next={fetchMoreData}
          hasMore={hasMore}
          loader={<Loader />}
        >
          <div className="my-10 grid grid-cols-1 lg:grid-cols-2 gap-4">
            {cards
              ?.filter(
                (item) =>
                  !!item?.attributes?.calloutbox &&
                  item?.attributes.slug !== 'dfds',
              )
              ?.map((cruise, index) => (
                <FullScreenCard
                  key={index}
                  data={cruise}
                  index={index}
                  page="discount"
                  onClick={onClick}
                  text="CRUISE EXCLUSIVE DISCOUNTS"
                >
                  <Discount item={cruise} showDiscountBtn={false} />
                </FullScreenCard>
              ))}
            {cards
              ?.filter(
                (item) =>
                  !!item?.attributes?.calloutbox &&
                  item?.attributes.slug === 'dfds',
              )
              ?.map((cruise, index) => (
                <FullScreenCard
                  key={index}
                  data={cruise}
                  index={index}
                  page="discount"
                  onClick={onClick}
                  text="CRUISE EXCLUSIVE DISCOUNTS"
                >
                  <Discount item={cruise} showDiscountBtn={false} />
                </FullScreenCard>
              ))}
          </div>
        </InfiniteScroll>
      </div>

      <div className="my-10">
        <section className="container mx-auto py-12 px-4 md:px-10">
          <div
            className={`mb-10 text-2xl md:text-[34px] font-normal text-center text-black ${playfair_display}`}
          >
            Other great Cruise Collective partner deals
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 text-center">
            {partner?.data?.map((item, indx) => (
              <ArticleCard key={indx} article={item} page="travel-partner" />
            ))}
          </div>
        </section>
      </div>

      <div className="my-10 md:px-10">
        <RenderComponent
          blocks={ctaInfoDiscount}
          componentType="blocks.call-to-actions"
          sort={3}
        />
      </div>
    </>
  );
};

export default ExclusiveDiscount;
