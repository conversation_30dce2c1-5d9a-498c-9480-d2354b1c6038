import DarkCruiseCollectiveImg from '@/components/DarkCruiseCollectiveImg';
import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import PrimaryButton from '@/components/PrimaryButton';
import Seo from '@/components/Seo';
import BgImage from '@/components/Shared/BgImage';
import PlayfairTextLgTextBase from '@/components/Typography/PlayfairTextLgTextBase';
import PlayfairTextSm from '@/components/Typography/PlayfairTextSm';
import { useAppDispatch } from '@/libs/hooks';
import { toggleLoginModal } from '@/libs/store/setting';
import { playfair_display } from '@/utils/fonts';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

const About = () => {
  const dispatch = useAppDispatch();
  const handleOnclick = () => {
    dispatch(toggleLoginModal());
  };
  const metaInfo = {
    metaTitle: 'Learn About Our Passion for Cruising',
    metaDescription:
      "At Cruise Collective, we help you explore the world through unforgettable cruise experiences. Discover our mission and commitment to providing the best deals.",
  };

  return (
    <>
      <Seo data={metaInfo} />

      <div className="py-10 md:py-[80px]">
        <div className="flex flex-col md:flex-row md:px-10">
          <div className="bg-image-height w-full md:w-4/6 relative">
            <BgImage bgImgUrl="/images/about/image-split.png" />
          </div>
          <div className="bg-orange-texture p-3 md:p-10 w-full md:w-2/6">
            <div className={`w-full md:max-w-[472px] text-[32px] text-black py-2 mt-4 text-3xl  ${playfair_display}`}>
              About the Cruise Collective
            </div>

            <div className="mt-5">
              <PlayfairTextLgTextBase
                className="text-left"
                text="We exist to help everyone with a special interest or personal passion to find the right cruise for them at the very best price."
              />
              <PlayfairTextLgTextBase
                className="text-left mt-4"
                text="How do we do this? We pool expertise and insight from right across the cruise community to show you the full extent of cruise possibilities and show you how you can do more of what you love - but at sea."
              />
              <PlayfairTextLgTextBase
                className="text-left mt-4"
                text="To help you choose, we bring you the best guides and opinion pieces from the people that matter alongside the best cruise itineraries and destination information. "
              />
              <PlayfairTextLgTextBase
                className="text-left mt-4"
                text="Because of our close relationships with partners right across the cruise community, from the major players to the emerging names, we provide our Members with the best discounts out there too.  "
              />
            </div>
            <div className="flex justify-end mt-5">
              <DarkCruiseCollectiveImg />
            </div>
          </div>
        </div>

        <div className="py-10 container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 items-center">
            <div className="p-4 md:p-[35px] xl:p-[50px]">
              <div className={`text-3xl md:text-4xl ${playfair_display}`}>
                Join the Cruise Collective today
              </div>

              <div className={`${playfair_display} text-black`}>
                <h2 className="text-xl pb-5 mt-6">As a member you’ll get:</h2>
                <ul className="custom-list-marker list-disc pl-5">
                  <li className="mt-4">
                    <strong>Exclusive Discounts: </strong>Priority to unbeatable
                    deals, significant discounts, and exciting competitions,
                    making your cruise experiences truly extraordinary.
                  </li>
                  <li className="mt-4">
                    <strong>Expert Opinion:</strong> Our writers are passionate
                    and experience cruisers who will tell you all the things you
                    need to know to make your choice and have an experience that
                    will linger in the memory forever.
                  </li>
                  <li className="mt-4">
                    <strong>Stay Informed:</strong> Stay up-to-date with the
                    latest news and products directly from cruise lines,
                    ensuring you're always right up to speed about upcoming
                    voyages, staying ahead of the chasing fleets.
                  </li>
                </ul>
              </div>
              <br></br>
              <div className="mt-12">
                <div className="py-3 px-8 ">
                  <PrimaryButton
                    textColor="text-black"
                    btnText="SIGN UP FOR FREE TODAY "
                    className="text-lg hover:bg-navy hover:text-cruise group/edit"
                    onClick={() => handleOnclick()}
                    iconColor="text-black group-hover/edit:text-cruise"
                  />
                </div>
              </div>
            </div>

            <div className="p-4 md:p-[35px] xl:p-[50px]">
              <img
                src="/images/about/image(12).png"
                alt="Your Image"
                className="w-861 h-432 flex-shrink-0"
              />
            </div>
          </div>
        </div>
        <div className="hidden bg-orange-texture p-4 md:p-[35px] xl:p-[50px]">
          <div className="text-3xl md:text-5xl">Our Partners</div>
          <div className="pt-2 pb-12">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="210"
              height="3"
              viewBox="0 0 210 3"
              fill="none"
            >
              <path
                d="M0.671875 1.79736L209.084 1.79738"
                stroke="#FF9A31"
                strokeWidth="1.73"
                strokeMiterlimit="10"
              />
            </svg>
          </div>

          <div className="flex flex-wrap justify-center">
            <Image
              src="/images/about/image.png"
              alt="Logo 1"
              width={250}
              height={100}
            />
            <Image
              src="/images/about/image(1).png"
              alt="Logo 2"
              width={250}
              height={250}
            />
            <Image
              src="/images/about/image(2).png"
              alt="Logo 3"
              width={250}
              height={250}
            />
            <Image
              src="/images/about/image(3).png"
              alt="Logo 4"
              width={250}
              height={250}
            />
            <Image
              src="/images/about/image(4).png"
              alt="Logo 5"
              width={250}
              height={250}
            />
            <Image
              src="/images/about/image(5).png"
              alt="Logo 6"
              width={250}
              height={250}
            />
            <Image
              src="/images/about/image(6).png"
              alt="Logo 7"
              width={250}
              height={250}
            />
          </div>

          <div className="mt-12 text-center font-semibold leading-100 tracking-[0.81px]">
            <button className="border text-lg border-[#FF9A31] py-3 px-8 hover:bg-[#FF9A31] hover:underline">
              Explore their latest offers
            </button>
          </div>
        </div>

        <div className="hidden pb-5 md:pb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 items-center">
            <div className="p-4 md:p-[35px] xl:p-[50px]">
              <div className="text-3xl md:text-5xl">Not a member yet?</div>
              <div className="pt-2 pb-12">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="210"
                  height="3"
                  viewBox="0 0 210 3"
                  fill="none"
                >
                  <path
                    d="M0.671875 1.79736L209.084 1.79738"
                    stroke="#FF9A31"
                    strokeWidth="1.73"
                    strokeMiterlimit="10"
                  />
                </svg>
              </div>
              <img
                src="/images/about/image(11).png"
                alt="Your Image"
                className="w-861 h-432 flex-shrink-0"
              />
            </div>

            <div className="p-4 md:p-[35px] xl:p-[50px]">
              <p className="text-base text-black py-2 mt-4">
                Some information about cruise collective... Lorem ipsum dolor
                sit amet, consectetur adipiscing elit. Donec sit amet ultricies
                felis. Cras sit amet ligula velit. Sed in tortor est. Fusce
                egestas at felis quis volutpat. Nam placerat auctor nisl, id
                efficitur urna. Nam non fermentum diam, vehicula euismod dui.
                Praesent finibus ultricies mollis. Integer accumsan varius
                sollicitudin. Vivamus sollicitudin efficitur lectus. Nunc sed
                elit vel metus porta facilisis. Etiam lacinia lacus a ante
                placerat, et placerat lorem convallis.
              </p>

              <br></br>
              <div className="mt-12">
                <button className="border text-lg border-[#FF9A31] py-3 px-8 hover:bg-[#FF9A31] hover:underline">
                  <Link href="/register">BECOME A MEMBER</Link>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default About;
