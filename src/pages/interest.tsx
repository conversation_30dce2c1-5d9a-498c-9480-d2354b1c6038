import FullScreenCard from '@/components/Card/FullScreenCard';
import DataLoadingFinishedText from '@/components/DataLoadingFinishedText';
import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import Loader from '@/components/Loader';
import { Loading } from '@/components/Loading';
import Seo from '@/components/Seo';
import PageHeader from '@/components/Shared/PageHeader';
import RenderComponent from '@/helpers/RenderComponent';
import useInfiniteScroll from '@/hooks/useInfiniteScroll';
import { ctaInfoDiscount } from '@/Interface/Dto';
import { getInterests } from '@/queries';
import { useRouter } from 'next/router';
import InfiniteScroll from 'react-infinite-scroll-component';

const interest = () => {
  const { isLoading, cards, hasMore, fetchMoreData } =
    useInfiniteScroll(getInterests);

  const data = {
    subTitle: 'Cruise by interest',
    title:
      'Experience luxury and adventure with our top cruise partners worldwide',
  };

  const metaInfo = {
    metaTitle: 'Find Cruises Tailored to Your Interests',
    metaDescription:
      'Discover cruises that match your hobbies with Cruise Collective. Whether for adventure, relaxation, or culture, we have the perfect cruise and exclusive deals.',
  };

  if (isLoading) return <Loading />;

  return (
    <>
      <Seo data={metaInfo} />

      <div className="container mx-auto px-4 md:px-10">
        <PageHeader data={data} />
      </div>

      <div className="md:px-10">
        <InfiniteScroll
          dataLength={cards?.length}
          next={fetchMoreData}
          hasMore={hasMore}
          loader={<Loader />}
        >
          <div className="my-10 grid grid-cols-1 lg:grid-cols-2 gap-4">
            {cards?.map((interest, index) => (
              <FullScreenCard
                key={index}
                data={interest}
                index={index}
                page="interest"
                text="CRUISE INTERESTS"
              />
            ))}
          </div>
        </InfiniteScroll>
      </div>

      <div className="mb-6 md:mb-10 mt-10 md:px-10">
        <RenderComponent
          blocks={ctaInfoDiscount}
          componentType="blocks.call-to-actions"
          sort={3}
        />
      </div>
    </>
  );
};

export default interest;
