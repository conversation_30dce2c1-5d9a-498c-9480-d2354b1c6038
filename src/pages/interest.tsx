import FullScreenCard from '@/components/Card/FullScreenCard';
import { baseUrl } from '@/utils';
import DataLoadingFinishedText from '@/components/DataLoadingFinishedText';
import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import Loader from '@/components/Loader';
import { Loading } from '@/components/Loading';
import Seo from '@/components/Seo';
import PageHeader from '@/components/Shared/PageHeader';
import RenderComponent from '@/helpers/RenderComponent';
import useInfiniteScroll from '@/hooks/useInfiniteScroll';
import { ctaInfoDiscount } from '@/Interface/Dto';
import { getInterests } from '@/queries';
import { useRouter } from 'next/router';
import InfiniteScroll from 'react-infinite-scroll-component';

const interest = ({ data }) => {
  const { attributes, id } = data;
  const seo = attributes?.seo || [];
  const title = attributes?.title || [];
  const subTitle = attributes?.subTitle || [];

  const { isLoading, cards, hasMore, fetchMoreData } =
    useInfiniteScroll(getInterests);

  if (isLoading) return <Loading />;

  return (
    <>
      <Seo data={seo} />

      <div className="container mx-auto px-4 md:px-10">
        <PageHeader data={{ title: title, subTitle: subTitle }} />
      </div>

      <div className="md:px-10">
        <InfiniteScroll
          dataLength={cards?.length}
          next={fetchMoreData}
          hasMore={hasMore}
          loader={<Loader />}
        >
          <div className="my-10 grid grid-cols-1 lg:grid-cols-2 gap-4">
            {cards?.map((interest, index) => (
              <FullScreenCard
                key={index}
                data={interest}
                index={index}
                page="interest"
                text="CRUISE INTERESTS"
              />
            ))}
          </div>
        </InfiniteScroll>
      </div>

      <div className="mb-6 md:mb-10 mt-10 md:px-10">
        <RenderComponent
          blocks={ctaInfoDiscount}
          componentType="blocks.call-to-actions"
          sort={3}
        />
      </div>
    </>
  );
};

export async function getServerSideProps() {
  const apiUrl = `${baseUrl}/api/landing-pages?populate=deep&filters[slug][$eq]=interest`;
  const res = await fetch(apiUrl);
  const { data } = await res.json();
  return {
    props: {
      data: data?.length ? data[0] : null,
    },
  };
}

export default interest;
