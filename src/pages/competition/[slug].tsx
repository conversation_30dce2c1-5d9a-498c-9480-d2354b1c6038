import React from 'react';
import { baseUrl } from '@/utils';

import Seo from '@/components/Seo';
import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import ArticleSlider from '@/components/ArticleSlider';
import { playfair_display } from '@/utils/fonts';
import RenderArticleEditor from '@/components/Shared/RenderArticleEditor';
import RenderComponent from '@/helpers/RenderComponent';
import ArticleCard from '@/components/Card/ArticleCard';
import ExploreMoreBySeaBlock from '@/components/PostPageContent/ExploreMoreBySeaBlock';
import AuthorBlock from '@/components/Shared/AuthorBlock';
import ArticleEditor from '@/components/Shared/ArticleEditor';

const filterBlocksByType = (blocks, type) => {
  if (!Array.isArray(blocks)) {
    return [];
  }
  return blocks.filter((item) => item.__component === type);
};

const CompetitionDetailPage = ({ data }) => {
  const { attributes } = data;

  // const createdAt = new Date(attributes?.date);
  // const options: any = { day: '2-digit', month: 'long', year: 'numeric' };
  // const formattedDate = new Intl.DateTimeFormat('en-US', options)?.format(
  //   createdAt,
  // );

  // const uppercaseFormattedDate = formattedDate.toUpperCase();

  const sliderData = {
    sliders: attributes?.featured_image?.data || [],
    heading: attributes?.title,
    excerpt: attributes?.excerpt,
    // date: uppercaseFormattedDate,
    btnText: 'VIEW MORE',
    video: attributes?.video,
  };

  // Filtering specific block types using the reusable function
  const splitImages = filterBlocksByType(
    attributes?.blocks,
    'blocks.split-image',
  );
  const gallery = filterBlocksByType(attributes?.blocks, 'blocks.gallery');
  const annotatedImage = filterBlocksByType(
    attributes?.blocks,
    'blocks.annotated-blocks',
  );

  const articleCta = filterBlocksByType(
    attributes?.blocks,
    'blocks.article-cta',
  );

  return (
    <>
      {data?.attributes?.seo ? (
        <Seo data={data?.attributes?.seo}></Seo>
      ) : (
        <DefaultMetaTitle title={data?.attributes?.title} />
      )}

      <ArticleSlider data={sliderData} bannerText="Competitions">
        {' '}
      </ArticleSlider>

      {attributes?.author_info && <AuthorBlock attributes={attributes} />}

      {attributes?.text_editor && (
        <ArticleEditor
          editorText={attributes?.text_editor}
          gallery={gallery}
          annotatedImage={annotatedImage}
          splitImages={splitImages}
          articleCta={articleCta}
        />
      )}

      <div className="my-12">
        <RenderComponent
          blocks={attributes?.blocks}
          componentType="blocks.call-to-actions"
          sort={2}
        />
      </div>

      {!!attributes?.related_articles?.articles?.data?.length && (
        <section className="container mx-auto py-10 md:py-[80px] px-4 md:px-10">
          <div
            className={`text-2xl md:text-[34px] text-black pb-4 md:pb-10 font-normal text-center ${playfair_display}`}
          >
            Related articles from the Collective
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-center">
            {attributes?.related_articles?.articles?.data?.map((item) => (
              <ArticleCard key={item.id} article={item} page="article" />
            ))}
          </div>
        </section>
      )}

      {attributes?.explore_more && (
        <section className="md:pt-[80px] md:px-10">
          <div
            className={`text-2xl md:text-[34px] text-black font-normal pb-4 md:pb-10 text-center ${playfair_display}`}
          >
            Explore more by sea
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <ExploreMoreBySeaBlock
              data={[attributes?.explore_more]}
              page="article"
            />
          </div>
        </section>
      )}

      <div className="my-10">
        <RenderComponent
          blocks={attributes?.blocks}
          componentType="blocks.call-to-actions"
          sort={3}
        />
      </div>
    </>
  );
};
export async function getServerSideProps(context) {
  const { params, query } = context;
  const slug = params.slug;
  const isPreview = query?.preview;

  const apiUrl = `${baseUrl}/api/competitions?populate=deep&filters[slug][$eq]=${slug}${
    isPreview ? '&publicationState=preview' : ''
  }`;
  const res = await fetch(apiUrl);
  const { data } = await res.json();

  return {
    props: {
      data: data?.length ? data[0] : null,
    },
  };
}
export default CompetitionDetailPage;
