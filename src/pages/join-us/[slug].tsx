import React from 'react';
import { GetServerSideProps } from 'next';
import { MasterOptions } from '@/layout/Master';
import RegistrationForm from '@/components/RegistrationForm';
import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import { getJoinUse } from '@/queries';
import RenderComponent from '@/helpers/RenderComponent';
import { playfair_display } from '@/utils/fonts';

export default function JoinUsPage({ data, slug }) {
  if (!data) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>No data found</p>
      </div>
    );
  }

  // Blocks and texts from Strapi
  const { blocks: originalBlocks = [], adestra_id } = data.attributes;
  const { id: campaign_id } = data;

  // Replace image-block with join-us-image-block for this page
  const blocks = originalBlocks.map((block) => {
    if (block.__component === 'blocks.image-block') {
      return {
        ...block,
        __component: 'blocks.join-us-image-block',
      };
    }
    if (block.__component === 'blocks.rich-text') {
      return {
        ...block,
        __component: 'blocks.join-us-rich-text',
      };
    }
    return block;
  });

  return (
    <>
      <DefaultMetaTitle title={data.attributes.brand || 'Join Us'} />
      <div className="flex items-center">
        <div className="mx-auto px-4 flex flex-col md:flex-row lg:pb-[4rem] pb-6 w-full">
          {/* Left side */}
          <div className="w-full md:w-1/2 md:p-5 lg:p-[75px] md:pr-7 pt-[40px] flex flex-col">
            <h1
              className={`text-black text-[40px] font-normal text-left ${playfair_display}`}
            >
              Join the collective
            </h1>
            <div className="border-solid border border-cruise w-32 mt-5" />

            {/* Blocks in order - only join-us-image-block and rich-text */}
            {blocks.map((block, idx) => {
              // Only show join-us-image-block and join-us-rich-text
              if (
                block.__component !== 'blocks.join-us-image-block' &&
                block.__component !== 'blocks.join-us-rich-text'
              ) {
                return null;
              }

              // Last image will be shown on the right side, don't render here
              const imageBlocks = blocks.filter(
                (b) => b.__component === 'blocks.join-us-image-block',
              );
              const lastImageBlock = imageBlocks[imageBlocks.length - 1];
              const isLastImage =
                lastImageBlock && block.id === lastImageBlock.id;

              if (
                isLastImage &&
                block.__component === 'blocks.join-us-image-block'
              ) {
                return null;
              }

              return (
                <div key={block.id} className="mt-5 md:pr-12">
                  <RenderComponent
                    blocks={[block]} // Only pass this specific block, not all blocks
                    componentType={block.__component}
                    sort={block.sort || 0}
                  />
                </div>
              );
            })}

            <RegistrationForm
              campaign_id={campaign_id}
              adestra_id={adestra_id}
            />
          </div>

          {/* Right side: Show the last image if available */}
          <div className="hidden md:flex w-full md:w-1/2 items-center justify-center">
            {(() => {
              const imageBlocks = blocks.filter(
                (b) => b.__component === 'blocks.join-us-image-block',
              );
              const lastImageBlock = imageBlocks[imageBlocks.length - 1];

              return lastImageBlock?.image?.data?.attributes?.url ? (
                <img
                  src={lastImageBlock.image.data.attributes.url}
                  alt={
                    lastImageBlock.image.data.attributes.name || 'Last Image'
                  }
                  className="w-full h-full object-cover rounded"
                />
              ) : null;
            })()}
          </div>
        </div>
      </div>
    </>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  const { slug } = context.params as { slug: string };

  try {
    const response = await getJoinUse(slug);

    if (response.data && response.data.length > 0) {
      return {
        props: {
          data: response.data[0],
          slug,
        },
      };
    } else {
      return {
        notFound: true,
      };
    }
  } catch (error) {
    return {
      props: {
        data: null,
        slug,
      },
    };
  }
};

JoinUsPage.masterOptions = {
  header: {
    actionBtnIsFilled: true,
  },
  footer: {},
  disableDwellPopup: true,
} as MasterOptions;
