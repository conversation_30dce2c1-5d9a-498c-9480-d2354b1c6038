import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import Seo from '@/components/Seo';
import ParallaxBox from '@/components/Shared/ParallaxBox';
import RenderComponent from '@/helpers/RenderComponent';
import { baseUrl } from '@/utils';
import { playfair_display } from '@/utils/fonts';

const InterestDetail = ({ data }) => {
  if(!data) return null;
  const { attributes, id } = data;
  const {
    seo,
    blocks = [],
    excerpt,
    title,
    headline,
    featured_image,
    video,
  } = attributes;

  return (
    <main>
      {seo ? <Seo data={seo}></Seo> : <DefaultMetaTitle title={title} />}

      <ParallaxBox
        image={featured_image?.data?.[0]?.attributes?.url}
        title={title}
        headline={headline}
        video={video}
        isHomePage={false}
      />

      <div className="container mx-auto px-4 md:px-10">
        <RenderComponent blocks={blocks} componentType="blocks.text-block" />
      </div>

      <div className="container mx-auto px-4 md:px-10 pt-10 pb-10 md:pb-[80px]">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 md:gap-[80px]">
          <RenderComponent blocks={blocks} componentType="blocks.promo" />
        </div>
      </div>

      <div className="container mx-auto px-4 md:px-10">
        <RenderComponent
          blocks={blocks}
          componentType="blocks.fancy-image-section"
          sort={1}
        />
      </div>

      <div className="container mx-auto px-4 md:px-10">
        <RenderComponent
          blocks={blocks}
          componentType="blocks.fancy-image-section"
          sort={2}
        />
      </div>

      <div className="container mx-auto px-4 md:px-10">
        <RenderComponent
          blocks={blocks}
          componentType="blocks.call-to-actions"
          sort={1}
        />
      </div>

      <div className="container mx-auto px-4 md:px-10">
        <RenderComponent
          blocks={blocks}
          componentType="blocks.fancy-image-section"
          sort={3}
        />
      </div>

      <div className="mt-5 md:px-10">
        <RenderComponent blocks={blocks} componentType="blocks.testimonial" />
      </div>

      <div className="container mx-auto px-4 md:px-10">
        <RenderComponent
          blocks={blocks}
          componentType="blocks.fancy-image-section"
          sort={4}
        />
      </div>

      <div className="container mx-auto px-4 md:px-10 my-4 md:my-12">
        <div className="md:mb-[160px]">
          <div
            className={`text-2xl md:text-[34px] text-black font-normal ${playfair_display} my-4 md:mt-[120px] text-center pb-4 md:pb-10 border-b`}
          >
            Get inspired - Some of our favourite experiences
          </div>

          <RenderComponent
            blocks={blocks}
            componentType="blocks.info-box-slider"
          />
        </div>
      </div>

      <div className="my-12 md:px-10">
        <RenderComponent
          blocks={blocks}
          componentType="blocks.call-to-actions"
          sort={2}
        />
      </div>

      {blocks?.some(
        (block) => block?.__component === 'blocks.related-articles',
      ) && (
      <section className="container mx-auto px-4 md:px-10 mt-10 md:mt-[160px]">
        <div
          className={`text-2xl md:text-[34px] text-black pb-4 md:pb-10 font-normal text-center ${playfair_display}`}
        >
          Related articles from the Collective
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 text-center">
          <RenderComponent
            blocks={blocks}
            componentType="blocks.related-articles"
          />
        </div>
      </section>
      )}

      {blocks?.some(
        (block) => block?.__component === 'blocks.explore-more-by-sea',
      ) && (
        <section className="md:pt-[160px] md:px-10">
          <div
            className={`text-2xl md:text-[34px] text-black font-normal pb-4 md:pb-10 text-center ${playfair_display}`}
          >
            Explore more by sea
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <RenderComponent
              blocks={blocks}
              componentType="blocks.explore-more-by-sea"
              page="interest"
            />
          </div>
        </section>
      )}

      <div className="my-10 md:px-10">
        <RenderComponent
          blocks={blocks}
          componentType="blocks.call-to-actions"
          sort={3}
        />
      </div>
    </main>
  );
};

export async function getServerSideProps(context) {
  const { params, query } = context;
  const slug = params.slug;
  const isPreview = query?.preview;

  const apiUrl = `${baseUrl}/api/interests?populate=deep&filters[slug][$eq]=${slug}${
    isPreview ? '&publicationState=preview' : ''
  }`;
  const res = await fetch(apiUrl);
  const { data } = await res.json();

  return {
    props: {
      data: data?.length ? data[0] : null,
    },
  };
}

export default InterestDetail;
