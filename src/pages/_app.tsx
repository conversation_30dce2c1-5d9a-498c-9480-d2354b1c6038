import '@/styles/globals.css';
import '@/styles/fonts.css';
import '@/styles/puzzle.css';

import '/node_modules/react-modal-video/scss/modal-video.scss';

import { QueryClient, QueryClientProvider } from 'react-query';

import type { AppProps } from 'next/app';
import Master from '@/layout/Master';
import { useState, useEffect } from 'react';
import { SessionProvider } from 'next-auth/react';
import { disableConsoleLogsInProduction } from '@/utils/consoleUtils'; // Import the utility
import { Provider } from 'react-redux';
import { wrapper } from '@/libs/store/store';
import { PersistGate } from 'redux-persist/integration/react';
import { ParallaxProvider } from 'react-scroll-parallax';
import { NavigationProvider } from '@/contexts/NavigationProvider';

disableConsoleLogsInProduction();

export default function App({
  Component,
  pageProps: { session, ...pageProps },
}: AppProps) {
  const [queryClient] = useState(() => new QueryClient());
  const { store, props } = wrapper.useWrappedStore(pageProps);

  useEffect(() => {
    // Google Analytics script
    const script = document.createElement('script');
    script.src = 'https://www.googletagmanager.com/gtag/js?id=G-4CKLFM137G';
    script.async = true;
    document.head.appendChild(script);

    const initScript = document.createElement('script');
    initScript.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-4CKLFM137G');
    `;
    document.head.appendChild(initScript);

    return () => {
      // Cleanup if needed
      document.head.removeChild(script);
      document.head.removeChild(initScript);
    };
  }, []);

  return (
    <SessionProvider session={session}>
      <Provider store={store}>
        <PersistGate loading={null} persistor={store.__persistor}>
          {() => (
            <QueryClientProvider client={queryClient}>
              <NavigationProvider>
                <Master
                  pageProps={pageProps}
                  masterOptions={(Component as any)?.masterOptions || {}}
                >
                  <ParallaxProvider>
                    <Component {...pageProps} />
                  </ParallaxProvider>
                </Master>
              </NavigationProvider>
            </QueryClientProvider>
          )}
        </PersistGate>
      </Provider>
    </SessionProvider>
  );
}
