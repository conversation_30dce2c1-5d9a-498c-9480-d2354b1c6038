import React from 'react';
import PageHeading from '@/components/PageHeading';
import useInfiniteScroll from '@/hooks/useInfiniteScroll';
import InfiniteScroll from 'react-infinite-scroll-component';
import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import Loader from '@/components/Loader';
import ArticleCard from '@/components/Card/ArticleCard';
import { getTravelPartner } from '@/queries';
import Seo from '@/components/Seo';

const TravelPartner = () => {
  const { cards, hasMore, fetchMoreData } = useInfiniteScroll(getTravelPartner);
  

  const pageHeaderData = {
    heading: 'Our Travel Partners',
    text: 'From savings on travel insurance to deals on portside parking here you’ll find our carefully curated selection of essential services enhancing every aspect of your journey. Our partners are here to elevate your travel experience and ensure a seamless voyage from start to finish. Welcome to enhanced travel with Cruise Collective.',
  };

  const metaInfo = {
    metaTitle: 'Exclusive Partner Cruise Offers & Discounts',
    metaDescription:
      "Discover exclusive offers from Cruise Collective’s partners. Enjoy special discounts on top cruises and unlock unbeatable deals tailored to your preferences.",
  };

  return (
    <>
      <Seo data={metaInfo} />
      <div className="lg:py-[75px] py-6 container mx-auto px-4 md:px-10">
        <PageHeading pageHeaderData={pageHeaderData} />

        <InfiniteScroll
          dataLength={cards?.length}
          next={fetchMoreData}
          hasMore={hasMore}
          loader={<Loader />}
        >
          <div className="container mx-auto my-10 space-y-12">
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                {cards?.slice(0, 2).map((article, index) => (
                  <ArticleCard
                    key={article.id}
                    article={article}
                    page="travel-partner"
                    className={`${index === 0 ? 'lg:col-span-2' : ''}`}
                  />
                ))}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                {cards?.slice(2, 4).map((article, index) => (
                  <ArticleCard
                    key={article.id}
                    article={article}
                    page="travel-partner"
                  />
                ))}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {cards?.slice(4).map((article) => (
                  <ArticleCard
                    key={article.id}
                    article={article}
                    page="travel-partner"
                  />
                ))}
              </div>
            </>
          </div>
        </InfiniteScroll>
      </div>
    </>
  );
};

export default TravelPartner;
