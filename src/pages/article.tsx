import ArticleCard from '@/components/Card/ArticleCard';
import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import Loader from '@/components/Loader';
import useInfiniteScroll from '@/hooks/useInfiniteScroll';
import { useRouter } from 'next/router';
import InfiniteScroll from 'react-infinite-scroll-component';
import { playfair_display } from '@/utils/fonts';
import { useQuery } from 'react-query';
import NavRight from '@/assets/svg/nav-right.svg';
import { useState } from 'react';
import clsx from 'clsx';
import { getAllArticleCategories, getArticles } from '@/queries';
import { ctaInfoBlocks } from '@/Interface/Dto';
import RenderComponent from '@/helpers/RenderComponent';
import Seo from '@/components/Seo';

const Article = () => {
  const router = useRouter();
  const { category } = router.query;
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const { isLoading, cards, total, hasMore, fetchMoreData } = useInfiniteScroll(
    getArticles,
    { content: 'inspiration_category', slug: category ? category : '' },
  );

  const { data, isLoading: isArticleLoading } = useQuery(
    'article-categories',
    () => getAllArticleCategories(),
    {
      refetchOnWindowFocus: false,
      enabled: true,
    },
  );

  const clearFilter = () => {
    router.push('/article');
  };

  const loadArticleWithCategory = (articleCategory) => {
    if (articleCategory?.attributes?.slug !== category) {
      router.push(`/article?category=${articleCategory?.attributes?.slug}`);
    } else {
      clearFilter();
    }
  };

  const metaInfo = {
    metaTitle: 'Expert Cruise Articles & Travel Tips',
    metaDescription:
      'Stay updated with Cruise Collective’s latest articles on cruise travel. Get expert advice, insider tips, and guides to enhance your cruising experience.',
  };

  return (
    <>
      <Seo data={metaInfo} />

      <div className="container mx-auto my-4 md:my-10 px-4 md:px-10">
        <div className="border-b border-[#DCDAD6] mb-12">
          {/* Header with Toggle Button */}
          <div
            className="text-black text-sm font-medium leading-[1.1] uppercase flex mb-3 cursor-pointer items-center"
            onClick={toggleExpand}
          >
            ARTICLE FILTERS
            <NavRight
              className={`ml-2 transform transition-transform duration-300 ${
                isExpanded ? '-rotate-90' : 'rotate-90'
              }`}
            />
          </div>
          {isExpanded && (
            <div className="flex gap-3 flex-wrap border-t border-[#DCDAD6]">
              {data?.data?.map((articleCategory) => (
                <div
                  key={articleCategory?.id}
                  onClick={() => {
                    loadArticleWithCategory(articleCategory);
                  }}
                  className={clsx(
                    'border text-black text-xs font-medium uppercase border-[#DCDAD6] px-3 py-1 rounded-full cursor-pointer mt-3 mb-3 ml-0 hover:border hover:border-brand',
                    {
                      'text-white bg-black':
                        articleCategory?.attributes?.slug === category,
                    },
                  )}
                >
                  # {articleCategory?.attributes?.name}{' '}
                  {articleCategory?.attributes?.slug === category && (
                    <span className="text-orange">X</span>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="container mx-auto mb-4 md:mb-10 px-4 md:px-10">
        <div
          className={`text-2xl md:text-[34px] text-black font-normal ${playfair_display} md:text-center`}
        >
          {category
            ? `Showing ${total} articles tagged ‘${category}’`
            : 'Latest articles from the collective'}
        </div>
      </div>

      <div className="container mx-auto px-4 md:px-10">
        <InfiniteScroll
          style={{ overflowX: 'hidden' }}
          dataLength={cards?.length}
          next={fetchMoreData}
          hasMore={hasMore}
          loader={<Loader />}
        >
          <div className="container mx-auto">
            {!category && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                  {cards?.slice(0, 2).map((article, index) => (
                    <ArticleCard
                      key={article.id}
                      article={article}
                      page="article"
                      className={`${index === 0 ? 'lg:col-span-2' : ''}`}
                    />
                  ))}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  {cards?.slice(2, 4).map((article, index) => (
                    <ArticleCard
                      key={article.id}
                      article={article}
                      page="article"
                    />
                  ))}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {cards?.slice(4).map((article) => (
                    <ArticleCard
                      key={article.id}
                      article={article}
                      page="article"
                    />
                  ))}
                </div>
              </>
            )}

            {category && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {cards?.map((article) => (
                  <ArticleCard
                    key={article.id}
                    article={article}
                    page="article"
                  />
                ))}
              </div>
            )}
          </div>

          {!cards.length && !isLoading && (
            <p className="text-2xl text-center"></p>
          )}
        </InfiniteScroll>
      </div>

      <div className="my-12 md:px-10">
        <RenderComponent
          blocks={ctaInfoBlocks}
          componentType="blocks.call-to-actions"
          sort={3}
        />
      </div>
    </>
  );
};

export default Article;
