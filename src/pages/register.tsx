import React from "react";
import { MasterOptions } from "@/layout/Master";
import RegistrationForm from "@/components/RegistrationForm";
import DefaultMetaTitle from "@/components/DefaultMetaTitle";

export default function RegisterPage() {

  return (
    <>
    </>
  )

  return (
    <>
      <DefaultMetaTitle title="Registration" />
      <div className="flex items-center ">
        <div className="mx-auto px-4 flex flex-col md:flex-row lg:pb-[4rem] pb-6">
          <div className="w-full md:w-1/2 md:p-5 lg:p-[75px] md:pr-7 pt-[40px]">
            <h1 className="text-black text-[40px] font-normal text-left">
              Join the collective
            </h1>
            <div className="border-solid border border-cruise w-32 mt-5" />

            <p className="mt-5 md:pr-12">
              Not a deals hub or a travel agent, but a place for people who love
              the adventure of Cruising to find everything they need to prepare
              for their next voyage and more…
              <br />
              <br /> Find adventure, luxury and exclusive savings with Cruise
              Collective; where members gain access to discounts, incredible
              competition prizes and insider knowledge designed to help you make
              the most of your next adventure at sea.
            </p>

            <RegistrationForm />
          </div>
          <div className="hidden md:block w-full md:w-1/2 bg-[url('/images/register-bg-img.jpg')] bg-center bg-no-repeat bg-cover">
          </div>
        </div>
      </div>
    </>
  );
}

RegisterPage.masterOptions = {
  header: {
    actionBtnIsFilled: true,
  },
} as MasterOptions;
