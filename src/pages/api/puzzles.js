import crypto from 'crypto';
import axios from 'axios';

export default async function handler(req, res) {
  const clientId = process.env.PUZZLE_CLIENT_ID;
  const apiKey = process.env.PUZZLE_API_KEY;
  const secretKey = process.env.PUZZLE_SECRET_KEY;

  const timestamp = Math.floor(Date.now() / 1000).toString();
  const signatureBase = `${apiKey}_${clientId}_${timestamp}_${secretKey}`;
  const signature = crypto
    .createHash('sha256')
    .update(signatureBase, 'utf8')
    .digest('base64');

  try {
    const response = await axios.post(
      'https://rest-api.puzzlerdigital.uk/api/Puzzle/collect',
      {
        puzzleDate: '2025-10-30',
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-Client-Id': clientId,
          'X-Api-Key': apiKey,
          'X-Timestamp': timestamp,
          'X-Signature': signature,
        },
      },
    );

    res.status(200).json(response.data);
  } catch (error) {
    console.error('API error:', error.response?.data || error.message);
    res.status(500).json({ error: 'Puzzle API request failed' });
  }
}
