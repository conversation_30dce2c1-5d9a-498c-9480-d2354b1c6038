import React, { useEffect } from "react";
import { useRouter } from "next/router";


const EmailConfirmation = () => {
  const router = useRouter();
  const { confirmation } = router.query;

  useEffect(() => {
    const emailConfirmationWithCode = async () => {
      router.push("/");
    };

    emailConfirmationWithCode();
  }, [confirmation, router]);

  return (
    <div>
      <main className="container mx-auto py-6 lg:py-12 px-4 md:px-10">
      </main>
    </div>
  );
};

export default EmailConfirmation;
