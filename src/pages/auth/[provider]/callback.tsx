import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import axios from 'axios';
import { loginUser } from '@/libs/store/user';
import { useAppDispatch, useAppSelector } from '@/libs/hooks';
import { baseUrl } from '@/utils';
import { setSSOData } from '@/libs/store/ssoRedirect';

const CallBack = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [user, setUser] = useState('');
  const dispatch = useAppDispatch();
  const provider = router.query.provider as string;
  const ssoRedirect = useAppSelector((state) => state.ssoRedirect);

  useEffect(() => {
    const handleProviderCallback = async (provider: string) => {
      const { id_token, access_token } = router.query; // Extract tokens from query params

      if (!id_token && !access_token) {
        dispatch(setSSOData({page: '/', data: {}}));
        setError(`Missing token for ${provider}`);
        setLoading(false);
        return;
      }

      try {
        // Determine the URL for the Strapi API based on the provider
        const url =
          provider === 'google'
            ? `${baseUrl}/api/auth/google/callback`
            : `${baseUrl}/api/auth/facebook/callback`;

        // Make the API request to Strapi to exchange the token for JWT
        const res = await axios.get(url, {
          params: provider === 'google' ? { id_token } : { access_token },
        });

        if (res.data.jwt) {
          // If JWT received, store it in localStorage or cookies
          localStorage.setItem('jwt', res.data.jwt);
          setUser(res.data.user); // Set the user data

          // Now update the NextAuth session manually
          dispatch(loginUser(res.data));

          if (ssoRedirect?.page) {
            router.push(ssoRedirect?.page);
          } else {
            //forward to homepage
            router.push('/');
          }

          setLoading(false);
        } else {
          dispatch(setSSOData({page: '/', data: {}}));
          setError('Authentication failed');
          setLoading(false);
        }
      } catch (error) {
        dispatch(setSSOData({page: '/', data: {}}));
        console.error(`Error during ${provider} callback:`, error);
        setError(`Failed to authenticate with Strapi via ${provider}`);
        setLoading(false);
      }
    };

    if (router.isReady) {
      handleProviderCallback(provider);
    }
  }, [router.isReady, router.query, router.pathname]);

  if (loading) {
    return <p>Loading...</p>;
  }

  if (error) {
    return <p>Error: {error}</p>;
  }

  if (user) {
    return <div>Welcome, you are logged in!</div>;
  }

  return null;
};

export default CallBack;
