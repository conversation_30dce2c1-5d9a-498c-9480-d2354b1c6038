import InfiniteScroll from 'react-infinite-scroll-component';
import useInfiniteScroll from '@/hooks/useInfiniteScroll';
import Loader from '@/components/Loader';
import FullScreenCard from '@/components/Card/FullScreenCard';
import PageHeader from '@/components/Shared/PageHeader';
import { getCruiseLines } from '@/queries';
import { ctaInfoDiscount } from '@/Interface/Dto';
import RenderComponent from '@/helpers/RenderComponent';
import Seo from '@/components/Seo';
const CruiseLineLanding = () => {
  const { isLoading, cards, hasMore, fetchMoreData } =
    useInfiniteScroll(getCruiseLines);
  const data = {
    subTitle: 'Cruise Lines',
    title:
      'Experience luxury, entertainment, and breathtaking destinations with our cruise partners.',
  };

  const metaInfo = {
    metaTitle: 'Explore Top Cruise Lines for Your Journey',
    metaDescription:
      'Compare the best cruise lines with Cruise Collective. Find your ideal line for adventure, featuring top services, amenities, and unbeatable deals just for you.',
  };

  return (
    <>
      <Seo data={metaInfo} />

      <div className="container mx-auto px-4 md:px-10">
        <PageHeader data={data} />
      </div>

      <div className="md:px-10">
        <InfiniteScroll
          dataLength={cards?.length} //This is important field to render the next data
          next={fetchMoreData}
          hasMore={hasMore}
          loader={<Loader />}
        >
          <div className="my-10 grid grid-cols-1 md:grid-cols-2  gap-4">
            {cards
              ?.filter((item) => item?.attributes.slug !== 'dfds')
              ?.map((cruise, index) => (
                <FullScreenCard
                  key={index}
                  data={cruise}
                  index={index}
                  page="cruise-line"
                  text="CRUISE LINES"
                ></FullScreenCard>
              ))}
            {cards
              ?.filter((item) => item?.attributes.slug === 'dfds')
              ?.map((cruise, index) => (
                <FullScreenCard
                  key={index}
                  data={cruise}
                  index={index}
                  page="cruise-line"
                  text="CRUISE LINES"
                ></FullScreenCard>
              ))}
          </div>
        </InfiniteScroll>
      </div>

      <div className="mb-6 md:mb-10 mt-10 md:px-10">
        <RenderComponent
          blocks={ctaInfoDiscount}
          componentType="blocks.call-to-actions"
          sort={3}
        />
      </div>
    </>
  );
};

export default CruiseLineLanding;
