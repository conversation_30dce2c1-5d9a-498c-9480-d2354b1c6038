import InfiniteScroll from 'react-infinite-scroll-component';
import { baseUrl } from '@/utils';
import useInfiniteScroll from '@/hooks/useInfiniteScroll';
import Loader from '@/components/Loader';
import FullScreenCard from '@/components/Card/FullScreenCard';
import PageHeader from '@/components/Shared/PageHeader';
import { getCruiseLines } from '@/queries';
import { ctaInfoDiscount } from '@/Interface/Dto';
import RenderComponent from '@/helpers/RenderComponent';
import Seo from '@/components/Seo';

const CruiseLineLanding = ({ data }) => {
  const { attributes, id } = data;
  const seo = attributes?.seo || [];
  const title = attributes?.title || [];
  const subTitle = attributes?.subTitle || [];

  const { isLoading, cards, hasMore, fetchMoreData } =
    useInfiniteScroll(getCruiseLines);
  return (
    <>
      <Seo data={seo} />

      <div className="container mx-auto px-4 md:px-10">
        <PageHeader data={{ title: title, subTitle: subTitle }} />
      </div>

      <div className="md:px-10">
        <InfiniteScroll
          dataLength={cards?.length} //This is important field to render the next data
          next={fetchMoreData}
          hasMore={hasMore}
          loader={<Loader />}
        >
          <div className="my-10 grid grid-cols-1 md:grid-cols-2  gap-4">
            {cards
              ?.filter((item) => item?.attributes.slug !== 'dfds')
              ?.map((cruise, index) => (
                <FullScreenCard
                  key={index}
                  data={cruise}
                  index={index}
                  page="cruise-line"
                  text="CRUISE LINES"
                ></FullScreenCard>
              ))}
            {cards
              ?.filter((item) => item?.attributes.slug === 'dfds')
              ?.map((cruise, index) => (
                <FullScreenCard
                  key={index}
                  data={cruise}
                  index={index}
                  page="cruise-line"
                  text="CRUISE LINES"
                ></FullScreenCard>
              ))}
          </div>
        </InfiniteScroll>
      </div>

      <div className="mb-6 md:mb-10 mt-10 md:px-10">
        <RenderComponent
          blocks={ctaInfoDiscount}
          componentType="blocks.call-to-actions"
          sort={3}
        />
      </div>
    </>
  );
};

export async function getServerSideProps() {
  const apiUrl = `${baseUrl}/api/landing-pages?populate=deep&filters[slug][$eq]=cruise-line`;
  const res = await fetch(apiUrl);
  const { data } = await res.json();
  return {
    props: {
      data: data?.length ? data[0] : null,
    },
  };
}

export default CruiseLineLanding;
