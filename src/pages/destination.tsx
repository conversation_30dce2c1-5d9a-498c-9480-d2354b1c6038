import { baseUrl } from '@/utils';
import FullScreenCard from '@/components/Card/FullScreenCard';
import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import Loader from '@/components/Loader';
import Seo from '@/components/Seo';
import PageHeader from '@/components/Shared/PageHeader';
import RenderComponent from '@/helpers/RenderComponent';
import useInfiniteScroll from '@/hooks/useInfiniteScroll';
import { ctaInfoDiscount } from '@/Interface/Dto';
import { getDestinations } from '@/queries';
import InfiniteScroll from 'react-infinite-scroll-component';

const destination = ({ data }) => {
  const { attributes, id } = data;
  const seo = attributes?.seo || [];
  const title = attributes?.title || [];
  const subTitle = attributes?.subTitle || [];

  const { isLoading, cards, hasMore, fetchMoreData } =
    useInfiniteScroll(getDestinations);

  return (
    <div>
      <Seo data={seo} />

      <div className="container mx-auto px-4 md:px-10">
        <PageHeader data={{ title: title, subTitle: subTitle }} />
      </div>

      <section className="md:px-10">
        <InfiniteScroll
          style={{ overflowX: 'hidden' }}
          dataLength={cards?.length} //This is important field to render the next data
          next={fetchMoreData}
          hasMore={hasMore}
          loader={<Loader />}
        >
          <div className="my-4 md:my-10 grid grid-cols-1 md:grid-cols-2 gap-4">
            {cards?.map((destination, index) => (
              <FullScreenCard
                key={destination.id}
                data={destination}
                index={index}
                page="destination"
                text="CRUISE DESTINATIONS"
              />
            ))}
          </div>
          {!cards.length && !isLoading && (
            <p className="text-2xl text-center"></p>
          )}
        </InfiniteScroll>
      </section>

      <div className="mb-6 md:mb-10 md:px-10">
        <RenderComponent
          blocks={ctaInfoDiscount}
          componentType="blocks.call-to-actions"
          sort={3}
        />
      </div>
    </div>
  );
};

export async function getServerSideProps() {
  const apiUrl = `${baseUrl}/api/landing-pages?populate=deep&filters[slug][$eq]=destination`;
  const res = await fetch(apiUrl);
  const { data } = await res.json();
  return {
    props: {
      data: data?.length ? data[0] : null,
    },
  };
}

export default destination;
