import { useState } from 'react';
import MemberPuzzlesBlock from '@/components/MemberPuzzlesBlock';
import { useAppDispatch, useAppSelector } from '@/libs/hooks';
import { toggleLoginModal } from '@/libs/store/setting';
import React from 'react';
import { getPuzzleMeta } from '@/utils/puzzleHelper';

export default function PuzzlePage() {
  // State management
  const [showModal, setShowModal] = useState(false);
  const [currentPuzzleIndex, setCurrentPuzzleIndex] = useState(0);
  const [puzzles, setPuzzles] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [guestPlayed, setGuestPlayed] = useState<string | null>(null);

  const getGuestPlayedFromStorage = () => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('guestPlayedPuzzle');
  };
  const setGuestPlayedToStorage = (val: string | null) => {
    if (typeof window === 'undefined') return;
    if (val) localStorage.setItem('guestPlayedPuzzle', val);
    else localStorage.removeItem('guestPlayedPuzzle');
  };

  // Redux state and actions
  const user = useAppSelector((state) => state.user);
  const dispatch = useAppDispatch();

  React.useEffect(() => {
    if (user.loggedIn) {
      setGuestPlayed(null);
      setGuestPlayedToStorage(null);
    } else {
      setGuestPlayed(getGuestPlayedFromStorage());
    }
  }, [user.loggedIn]);

  // Fetch puzzles on mount
  React.useEffect(() => {
    setLoading(true);
    fetch('/api/puzzles')
      .then((res) => res.json())
      .then((data) => {
        setPuzzles(data);
        setLoading(false);
      });
  }, []);

  /**
   * Loads the puzzle script
   * @returns {Promise} Resolves when script is loaded
   */
  const loadPuzzleScript = (): Promise<void> => {
    return new Promise<void>((resolve) => {
      if ((window as any).pml) return resolve();
      const script = document.createElement('script');
      script.src = '/pml.js';
      script.type = 'text/javascript';
      script.async = true;
      script.onload = () => resolve();
      document.body.appendChild(script);
    });
  };

  /**
   * Loads a specific puzzle by index
   * @param {Array} puzzleData - Array of puzzle data
   * @param {number} index - Index of the puzzle to load
   */
  const loadPuzzle = (puzzleData, index) => {
    const selectedPuzzle = puzzleData[index];
    if (!window.pml || !selectedPuzzle) return;

    resetGameCanvas();

    setTimeout(() => {
      window.pml.startGame('gameCanvas', {
        starting_puzzle: selectedPuzzle,
      });
    }, 50);
  };

  /**
   * Resets the game canvas by creating a fresh canvas element
   */
  const resetGameCanvas = () => {
    const gameContainer = document.querySelector('.game-container');
    if (!gameContainer) return;

    gameContainer.innerHTML = '';

    const newCanvas = document.createElement('div');
    newCanvas.id = 'gameCanvas';
    newCanvas.className = 'w-full h-full';
    gameContainer.appendChild(newCanvas);
  };

  /**
   * Opens modal and loads puzzle script + selected puzzle
   */
  const openPuzzleModal = async (index: number) => {
    const puzzleId = puzzles[index]?.pml_id || String(index);
    if (!user.loggedIn) {
      if (guestPlayed && guestPlayed !== puzzleId) {
        dispatch(toggleLoginModal());
        return;
      }
      if (!guestPlayed) {
        setGuestPlayed(puzzleId);
        setGuestPlayedToStorage(puzzleId);
      }
    }
    setCurrentPuzzleIndex(index);
    setShowModal(true);
    setTimeout(async () => {
      await loadPuzzleScript();
      loadPuzzle(puzzles, index);
    }, 100);
  };

  /**
   * Closes the modal and cleans up resources
   */
  const closeModal = () => {
    setShowModal(false);
    setCurrentPuzzleIndex(0);

    const script = document.querySelector('script[src="/pml.js"]');
    if (script) {
      document.body.removeChild(script);
    }
  };

  return (
    <section className="puzzle-section">
      <div className="w-full py-8">
        <MemberPuzzlesBlock button={false} />
      </div>
      <div className="puzzle-container max-w-6xl mx-auto">
        <div className="text-center px-10">
          {loading ? (
            <div className="py-10">Loading...</div>
          ) : (
            <div className="shadow-lg rounded-md border-l-8 border-cruise px-6 py-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4">
                {puzzles.map((puzzle: any, idx: number) => {
                  const { puzzleName, iconPath } = getPuzzleMeta(puzzle);

                  return (
                    <button
                      key={puzzle.pml_id || idx}
                      className={
                        `flex items-center justify-between border-b border-r border-gray-200 px-4 py-3 hover:bg-gray-50 transition ` +
                        ((idx + 1) % 4 === 0 ? 'last:border-r-0 ' : '') +
                        (Math.floor(idx / 4) ===
                        Math.floor((puzzles.length - 1) / 4)
                          ? ' border-b-0'
                          : '')
                      }
                      onClick={() => openPuzzleModal(idx)}
                      style={{ minHeight: '80px' }}
                    >
                      <div className="flex items-center">
                        <img
                          src={iconPath}
                          alt={puzzle.name}
                          className="w-14 h-14 object-contain rounded mr-4"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src =
                              '/images/default.jpg';
                          }}
                        />
                        <span className="font-medium text-gray-900 text-sm md:text-base">
                          {puzzleName}
                        </span>
                      </div>
                      <span className="text-gray-400 text-xl">{'>'}</span>
                    </button>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>

      {showModal && (
        <PuzzleModal
          currentPuzzleIndex={currentPuzzleIndex}
          totalPuzzles={puzzles.length || 5}
          puzzleName={puzzles[currentPuzzleIndex]?.name || ''}
          onClose={closeModal}
        />
      )}
    </section>
  );
}

/**
 * Puzzle Modal Component
 */
interface PuzzleModalProps {
  currentPuzzleIndex: number;
  totalPuzzles: number;
  puzzleName: string;
  onClose: () => void;
}
const PuzzleModal: React.FC<PuzzleModalProps> = ({
  currentPuzzleIndex,
  totalPuzzles,
  puzzleName,
  onClose,
}) => {
  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-navy bg-opacity-70"
      onClick={onClose}
    >
      <div
        className="bg-white px-4 md:px-10 py-10 rounded-none md:rounded-lg shadow-lg w-full h-full md:h-full md:w-full relative"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between mb-4">
          <div className="flex items-center">
            <h2 className="text-2xl">
              Puzzle {currentPuzzleIndex + 1}/{totalPuzzles} – {puzzleName}
            </h2>
          </div>
          <button
            className="text-gray-500 hover:text-gray-700 text-3xl hover:text-red"
            onClick={onClose}
          >
            &times;
          </button>
        </div>
        <div className="game-container w-full h-[calc(100%-60px)]">
          <div id="gameCanvas" className="w-full h-full" />
        </div>
      </div>
    </div>
  );
};
