import React from 'react';
import { useQuery } from 'react-query';
import { getTermsConditionData } from '@/queries';
import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import { playfair_display } from '@/utils/fonts';
import styles from '../styles/article.module.css';

const TermsCondition = () => {
  const { data } = useQuery('TermsCondition', () => getTermsConditionData(), {
    refetchOnWindowFocus: false,
    enabled: true,
  });

  return (
    <>
      <DefaultMetaTitle title="Terms and Conditions" />
      <div className="container py-10 md:py-[80px] mx-auto px-4 md:px-10">
        <div
          className={`${styles.articleContainer} ${playfair_display}`}
          dangerouslySetInnerHTML={{
            __html: data?.description,
          }}
        ></div>
      </div>
    </>
  );
};

export default TermsCondition;
