import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import { getCompetitionTermsConditionData } from '@/queries';
import React from 'react';
import { useQuery } from 'react-query';
import styles from '../styles/article.module.css';
import { playfair_display } from '@/utils/fonts';

const CompetitionTermsAndConditions = () => {
  const { data } = useQuery(
    'CompetitionTermsCondition',
    () => getCompetitionTermsConditionData(),
    {
      refetchOnWindowFocus: false,
      enabled: true,
    },
  );

  return (
    <>
      <DefaultMetaTitle title="Competition Terms and Conditions" />
      <div className="px-4 md:px-10 container mx-auto">
        <div
          dangerouslySetInnerHTML={{
            __html: data?.description,
          }}
          className={`${styles.articleContainer} ${playfair_display}`}
        ></div>
      </div>
    </>
  );
};
export default CompetitionTermsAndConditions;
