import { useRouter } from 'next/router';
import Head from 'next/head';

export default function HurtigrutenPage() {
  const router = useRouter();
  const slug = router.query.slug;

  if (!slug) return null;

  const pathArray = Array.isArray(slug) ? slug : [slug];
  const foleonUrl = `https://cruise-collective.foleon.com/hurtigruten/${pathArray.join(
    '/',
  )}/`;

  return (
    <>
      <Head>
        <title>{pathArray.join(' – ')}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <iframe
        src={foleonUrl}
        style={{
          border: 'none',
          width: '100vw',
          height: '100vh',
        }}
        allowFullScreen
      />
    </>
  );
}
