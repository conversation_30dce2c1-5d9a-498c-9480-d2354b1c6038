import styles from '../../styles/article.module.css';
import Seo from '@/components/Seo';
import { baseUrl, formatDate } from '@/utils';
import React, { useEffect, useState } from 'react';
import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import { playfair_display } from '@/utils/fonts';
import RenderArticleEditor from '@/components/Shared/RenderArticleEditor';
import RenderComponent from '@/helpers/RenderComponent';
import ArticleCard from '@/components/Card/ArticleCard';
import ExploreMoreBySeaBlock from '@/components/PostPageContent/ExploreMoreBySeaBlock';
import ArticleSlider from '@/components/ArticleSlider';
import AuthorBlock from '@/components/Shared/AuthorBlock';
import ArticleEditor from '@/components/Shared/ArticleEditor';
import { useAppDispatch } from '@/libs/hooks';
import { useSelector } from 'react-redux';
import { RootState } from '@/libs/store/store';
import ArrowButton from '@/components/Shared/ArrowButton';
import clsx from 'clsx';
import { toggleLoginModal } from '@/libs/store/setting';
import Link from 'next/link';

const filterBlocksByType = (blocks, type) => {
  if (!Array.isArray(blocks)) {
    return [];
  }
  return blocks.filter((item) => item.__component === type);
};

const TravelPartnerDetails = ({ data }) => {
  const dispatch = useAppDispatch();
  const session = useSelector((state: RootState) => state.user);
  const { attributes } = data;
  const [isOpenLoginModal, setIsLoginModal] = useState(false);

  const sliderData = {
    sliders: attributes?.featured_image?.data || [],
    heading: attributes?.title,
    excerpt: attributes?.excerpt,
    btnText: 'VIEW MORE',
    video: attributes?.video,
  };

  // Filtering specific block types using the reusable function
  const splitImages = filterBlocksByType(
    attributes?.blocks,
    'blocks.split-image',
  );
  const gallery = filterBlocksByType(attributes?.blocks, 'blocks.gallery');
  const annotatedImage = filterBlocksByType(
    attributes?.blocks,
    'blocks.annotated-blocks',
  );

  const articleCta = filterBlocksByType(
    attributes?.blocks,
    'blocks.article-cta',
  );

  const handleOffer = () => {
    setIsLoginModal(true);
    dispatch(toggleLoginModal());
  };

  useEffect(() => {
    if (session.loggedIn && isOpenLoginModal) {
      window.open(attributes?.affiliate_link, '_blank');
    }
  }, [session.loggedIn]);

  return (
    <>
      {data?.attributes?.seo ? (
        <Seo data={data?.attributes?.seo}></Seo>
      ) : (
        <DefaultMetaTitle title={data?.attributes?.title} />
      )}

      <ArticleSlider data={sliderData} bannerText="Partner Offers">
        {' '}
      </ArticleSlider>

      {attributes?.author_info && <AuthorBlock attributes={attributes} />}

      {attributes?.description && (
        <ArticleEditor
          editorText={attributes?.description}
          gallery={gallery}
          annotatedImage={annotatedImage}
          splitImages={splitImages}
          articleCta={articleCta}
        />
      )}

      {attributes?.affiliate_link && (
        <div className="pt-10 md:pt-[80px] w-full md:w-3/4 mx-auto">
          <div className={`px-4 lg:px-10 xl:px-[80px]`}>
            {session?.loggedIn ? (
              <>
                <Link target="_blank" href={attributes?.affiliate_link}>
                  <button
                    className={clsx(
                      `uppercase w-full flex mt-4 flex-nowrap items-center justify-center px-4 py-3 font-medium group-hover/edit bg-cruise text-black text-sm hover:bg-navy hover:text-cruise group`,
                    )}
                  >
                    Claim Discount
                    <span className="text-black group-hover:text-cruise ml-1.5">
                      <ArrowButton />
                    </span>
                  </button>
                </Link>
                {attributes?.coupon_code && (
                  <div className="text-center mt-2" style={{ padding: '10px' }}>
                    <p className="text-sm">
                      <span className="font-medium uppercase">
                        Coupon Code:
                      </span>{' '}
                      <span className="font-medium">
                        {attributes?.coupon_code}
                      </span>
                    </p>
                  </div>
                )}
              </>
            ) : (
              <button
                onClick={() => handleOffer()}
                className={clsx(
                  `uppercase flex w-full flex-nowrap items-center justify-center px-4 py-3 font-medium group-hover/edit bg-cruise text-black text-sm hover:bg-navy hover:text-cruise group`,
                )}
              >
                Click here to visit our partner and apply this offer
                <span className="text-black group-hover:text-cruise ml-1.5">
                  <ArrowButton />
                </span>
              </button>
            )}
          </div>
        </div>
      )}

      <div className="my-12">
        <RenderComponent
          blocks={attributes?.blocks}
          componentType="blocks.call-to-actions"
          sort={2}
        />
      </div>

      {!!attributes?.related_articles?.articles?.data?.length && (
        <section className="container mx-auto py-10 md:py-[80px] px-4 md:px-10">
          <div
            className={`text-2xl md:text-[34px] text-black pb-4 md:pb-10 font-normal text-center ${playfair_display}`}
          >
            Related articles from the Collective
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-center">
            {attributes?.related_articles?.articles?.data?.map((item) => (
              <ArticleCard key={item.id} article={item} page="article" />
            ))}
          </div>
        </section>
      )}

      {attributes?.explore_more && (
        <section className="md:pt-[80px] md:px-10">
          <div
            className={`text-2xl md:text-[34px] text-black font-normal pb-4 md:pb-10 text-center ${playfair_display}`}
          >
            Explore more by sea
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <ExploreMoreBySeaBlock
              data={[attributes?.explore_more]}
              page="article"
            />
          </div>
        </section>
      )}

      <div className="my-10">
        <RenderComponent
          blocks={attributes?.blocks}
          componentType="blocks.call-to-actions"
          sort={3}
        />
      </div>
    </>
  );
};

export async function getServerSideProps(context) {
  const { params, query } = context;
  const slug = params.slug;
  const isPreview = query?.preview;

  const apiUrl = `${baseUrl}/api/travel-partner-offers?populate=deep&filters[slug][$eq]=${slug}${
    isPreview ? '&publicationState=preview' : ''
  }`;
  const res = await fetch(apiUrl);

  const { data } = await res.json();

  return {
    props: {
      data: data?.length ? data[0] : null,
    },
  };
}

export default TravelPartnerDetails;
