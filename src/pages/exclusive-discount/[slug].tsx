import { baseUrl } from '@/utils';
import Seo from '@/components/Seo';
import DefaultMetaTitle from '@/components/DefaultMetaTitle';
import Link from 'next/link';
import UseDiscountInfo from '@/components/Promo/UseDiscountInfo';
import { playfair_display } from '@/utils/fonts';
import PrimaryButton from '@/components/PrimaryButton';
import clsx from 'clsx';
import ArrowButton from '@/components/Shared/ArrowButton';
import 'swiper/css/navigation';
import 'swiper/css/bundle';
import { Pagination } from 'swiper';
import { Swiper, SwiperSlide } from 'swiper/react';
import OfferCard2 from '@/components/Card/OfferCard2';
import { useAppDispatch, useAppSelector } from '@/libs/hooks';
import { useState } from 'react';
import { toggleLoginModal } from '@/libs/store/setting';

// Constants for reusable styles and configurations
const CONTAINER_STYLES = 'container mx-auto px-4 md:px-10 mt-5';
const HEADING_STYLES = `text-2xl md:text-[34px] ${playfair_display} text-center font-normal`;
const BUTTON_STYLES = 'text-sm hover:bg-navy hover:text-cruise min-w-[175px]';
const CARD_STYLES = 'bg-orange p-3 w-full border border-dashed border-paper';

const ExclusiveDiscountDetail = ({ data, latestData }) => {
  const { attributes } = data;
  const { excerpt, title: heading, calloutbox, logo, extra_page } = attributes;
  const [buttonText, setButtonText] = useState('Copy to clipboard');
  const [isShowHowWorks, setIsShowHowWorks] = useState(false);
  const dispatch = useAppDispatch();

  const loggedIn = useAppSelector((state) => state.user?.loggedIn);

  const {
    saving,
    description,
    button_text,
    discount_label,
    discount_button_label,
    sub_heading,
    terms_and_condition: terms,
    button_url,
    title,
    how_to_use,
  } = calloutbox;

  const { carousel = {}, seo = {} } = extra_page || {};
  const isCarousel = carousel?.data?.length > 0;

  const handleButtonClick = () => {
    if (loggedIn) {
      navigator.clipboard.writeText(description).then(() => {
        setButtonText('Copied!');
        setTimeout(() => {
          setButtonText('Copy to clipboard');
        }, 1200);
      });
    } else {
      dispatch(toggleLoginModal());
    }
  };

  const toggleHowDoesItWork = () => {
    setIsShowHowWorks((prev) => !prev);
  };

  return (
    <main>
      {seo ? <Seo data={seo} /> : <DefaultMetaTitle title={title} />}
      <div className={CONTAINER_STYLES}>
        <div
          className={`grid gap-10 ${
            isCarousel ? 'grid-cols-1 xl:grid-cols-2' : 'grid-cols-1'
          }`}
        >
          {isCarousel && (
            <div className="order-2 xl:order-1 h-full">
              <Carousel carousel={carousel} />
            </div>
          )}
          <div
            className={`order-1 xl:order-2 ${
              !isCarousel ? 'max-w-4xl mx-auto w-full' : ''
            }`}
          >
            <DiscountContent
              heading={heading}
              excerpt={excerpt}
              logo={logo}
              discount_label={discount_label}
              discount_button_label={discount_button_label}
              saving={saving}
              isShowHowWorks={isShowHowWorks}
              title={title}
              description={description}
              button_text={buttonText}
              sub_heading={sub_heading}
              terms={terms}
              button_url={button_url}
              handleButtonClick={handleButtonClick}
              isCarousel={isCarousel}
              how_to_use={how_to_use}
              loggedIn={loggedIn}
              toggleHowDoesItWork={toggleHowDoesItWork}
            />
          </div>
        </div>
        <OtherOffers latestData={latestData} />
      </div>
    </main>
  );
};

// Carousel Component
const Carousel = ({ carousel }) => (
  <div className="w-full h-full relative">
    <Swiper
      pagination={{ clickable: true }}
      modules={[Pagination]}
      className="rounded-lg shadow-lg h-full"
    >
      {carousel.data.map((src, index) => (
        <SwiperSlide key={index}>
          <div className="relative w-full aspect-[4/3] h-full flex justify-center items-center">
            <img
              src={src?.attributes?.url}
              alt={`Slide ${index + 1}`}
              className="rounded-lg w-full h-full object-cover"
            />
          </div>
        </SwiperSlide>
      ))}
    </Swiper>
    <style jsx global>{`
      .swiper-pagination {
        position: absolute;
        bottom: 50px !important;
        left: 50%;
        display: flex;
        justify-content: center;
      }
      .swiper {
        height: 100%;
      }
      .swiper-slide {
        height: auto;
      }
    `}</style>
  </div>
);

// Discount Content Component
const DiscountContent = ({
  heading,
  excerpt,
  logo,
  saving,
  discount_label,
  discount_button_label,
  isShowHowWorks,
  title,
  description,
  button_text,
  sub_heading,
  terms,
  button_url,
  handleButtonClick,
  isCarousel,
  how_to_use,
  loggedIn,
  toggleHowDoesItWork,
}) => (
  <div className="w-full">
    <div className="flex flex-col">
      <h2 className={HEADING_STYLES}>{heading}</h2>
      <div
        className={`text-center text-black font-normal mt-5 ${playfair_display}`}
      >
        {excerpt}
      </div>
    </div>
    <div className="mt-10 flex flex-col lg:flex-row gap-6 lg:gap-2.5 w-full">
      <div className="lg:w-[195px] flex-shrink-0">
        <LogoAndSaving logo={logo} saving={saving} discount_label={discount_label} discount_button_label={discount_button_label} />
      </div>
      <div className={`${CARD_STYLES} flex-grow`}>
        <div className="p-6 border-cruise border h-full">
          {!isShowHowWorks ? (
            <DiscountDetails
              title={title}
              description={description}
              button_text={button_text}
              sub_heading={sub_heading}
              handleButtonClick={handleButtonClick}
              loggedIn={loggedIn}
            />
          ) : (
            <HowItWorks how_to_use={how_to_use} />
          )}
          <div className="flex md:hidden mt-12 justify-center">
            <PrimaryButton
              className="text-sm"
              textColor="text-black"
              btnText={isShowHowWorks ? 'Back to code' : 'How it works'}
              isArrowShow={false}
              onClick={toggleHowDoesItWork}
            />
          </div>
        </div>
      </div>
    </div>
    {!isShowHowWorks && <HowToUseSection how_to_use={how_to_use} />}
    <TermsAndConditions
      terms={terms}
      button_url={button_url}
      button_text={button_text}
      loggedIn={loggedIn}
    />
  </div>
);

// Logo and Saving Component
const LogoAndSaving = ({ logo, saving, discount_label, discount_button_label }) => (
  <div className="flex flex-col gap-4 justify-center items-center">
    <div
      className="relative text-center items-center justify-between flex flex-col bg-no-repeat bg-center bg-cover p-3 hidden md:flex"
      style={{
        backgroundImage: "url('/images/stamp-bg.png')",
        width: '195px',
        height: '195px',
      }}
    >
      <div className="max-h-[60px] w-full">
        {logo?.data?.attributes?.url && (
          <img
            src={logo?.data?.attributes?.url}
            alt="stamp"
            className="relative h-full z-20 w-full object-contain"
          />
        )}
      </div>
      <p className="text-orange text-3xl md:text-6xl font-semibold relative z-20 transform">
        {saving}
      </p>
      <p className="text-sm text-orange relative z-20 transform translate-y-[-40%] mt-1 uppercase">
        {discount_label || 'MEMBERS DISCOUNT'}
      </p>
    </div>
    <div className="flex gap-4 w-full md:hidden items-center">
      <div className="w-3/5 md:w-full">
        {logo?.data?.attributes?.url && (
          <img
            src={logo?.data?.attributes?.url}
            alt="stamp"
            className="relative z-20 transform translate-y-[-20%] px-4"
          />
        )}
      </div>
      <div className="w-2/5 md:w-full">
        <p className="text-brand text-[40px] text-center font-bold normal-case">
          {saving}
        </p>
        <p className="text-xs text-orange text-center uppercase">
          {discount_label || 'MEMBERS DISCOUNT'}
        </p>
      </div>
    </div>
  </div>
);

// Discount Details Component
const DiscountDetails = ({
  title,
  description,
  button_text,
  sub_heading,
  handleButtonClick,
  loggedIn,
}) => (
  <>
    <h3
      className={`md:text-2xl ${playfair_display} text-center font-normal text-black text-xl`}
    >
      {title}
    </h3>
    <div className="flex justify-center items-center flex-col md:flex-row gap-4 mt-2">
      <div
        className={`text-4xl md:text-[52px] text-white font-bold ${
          !loggedIn ? 'blur-md select-none pointer-events-none' : ''
        }`}
      >
        {!loggedIn ? 'ABCDEF' : description ?? 'none'}
      </div>

      <PrimaryButton
        className={BUTTON_STYLES}
        textColor="text-black"
        btnText={loggedIn ? button_text : 'LOGIN TO REDEEM'}
        isArrowShow={false}
        onClick={handleButtonClick}
      />
    </div>
    <p className="uppercase text-center text-xs text-black font-medium mt-2">
      {sub_heading}
    </p>
  </>
);

// How It Works Component
const HowItWorks = ({ how_to_use }) => (
  <>
    {how_to_use?.map((item, idx) => (
      <div key={idx} className="mb-4">
        <div className="text-sm mb-1 font-medium text-black uppercase">
          {item?.title}
        </div>
        <p
          className={`text-sm text-left font-normal text-black ${playfair_display}`}
        >
          {item?.description}
        </p>
      </div>
    ))}
  </>
);

// How to Use Section Component
const HowToUseSection = ({ how_to_use }) => (
  <div className="mt-10 mb-10 hidden md:block">
    <div className={`font-normal text-black text-2xl ${playfair_display}`}>
      How to use your discount
    </div>
    <div className="grid grid-cols-1 md:grid-cols-3 justify-center mt-10 gap-4 md:gap-10 pb-10 border-b">
      {how_to_use?.map((item, idx) => (
        <UseDiscountInfo key={idx} data={item} />
      ))}
    </div>
  </div>
);

// Terms and Conditions Component
const TermsAndConditions = ({ terms, button_url, button_text, loggedIn }) => (
  <div className="mt-12 md:mt-4 flex flex-col md:flex-row gap-3 pb-8">
    <div
      className={`text-sm md:w-1/2 w-full ${playfair_display} font-normal`}
      style={{ color: 'rgba(0, 0, 0, 0.60)' }}
    >
      For full details and terms of use, see the vendors
      <Link
        target="_blank"
        className="ml-1 underline text-black"
        href={`/terms-and-conditions/${terms?.data?.attributes?.slug}`}
      >
        terms and conditions
      </Link>
    </div>

    {loggedIn && (
      <div className="md:w-1/2 w-full md:flex justify-end">
        <Link target="_blank" href={button_url}>
          <button
            className={clsx(
              `uppercase flex flex-nowrap items-center justify-center px-4 py-3 font-medium group-hover/edit bg-cruise text-black text-sm hover:bg-navy hover:text-cruise group`,
            )}
          >
            Redeem this offer
            <span className="text-black group-hover:text-cruise ml-1.5">
              <ArrowButton />
            </span>
          </button>
        </Link>
      </div>
    )}
  </div>
);

// Other Offers Component
const OtherOffers = ({ latestData }) => (
  <div className="mt-14">
    <h3
      className={`md:text-2xl ${playfair_display} text-center font-normal text-black text-xl`}
    >
      Other cruise line offers
    </h3>
    <div className="grid grid-cols-1 md:grid-cols-4 mt-10 gap-4 md:gap-10">
      {latestData.map((item, index) => (
        <OfferCard2 key={index} data={item} />
      ))}
    </div>
  </div>
);

// Fetch data on the server side
export async function getServerSideProps(context) {
  const { params, query } = context;
  const slug = params.slug;
  const isPreview = query?.preview;

  const apiUrl = `${baseUrl}/api/cruise-lines?populate[0]=extra_page&populate[1]=calloutbox&&populate[2]=featured_image&populate[5]=calloutbox.how_to_use&populate[6]=logo&populate[7]=extra_page.carousel&populate[8]=calloutbox.terms_and_condition&populate[9]=extra_page.seo&filters[$or][0][slug][$eq]=${slug}&filters[$or][1][id][$ne]=0&sort=createdAt:desc&pagination[limit]=8${
    isPreview ? '&publicationState=preview' : ''
  }`;

  const res = await fetch(apiUrl);
  const { data } = await res.json();

  const selectedData =
    data.find((item) => item.attributes.slug === slug) || null;
  const latestData = data.filter((item) => item.attributes.slug !== slug);

  return {
    props: {
      data: selectedData,
      latestData,
    },
  };
}

export default ExclusiveDiscountDetail;
