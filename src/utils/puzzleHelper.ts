/**
 * Build puzzle display name and icon path.
 *
 * @param puzzle - Puzzle object from API
 * @returns { puzzleName: string, iconPath: string }
 */
export function getPuzzleMeta(puzzle: any) {
  const level = puzzle?.game_data?.difficultyLevel;
  const hasLevel = level && level.toUpperCase() !== 'UNDEFINED';

  // Display name (e.g. "Sudoku - Easy", "Crossword")
  const puzzleName = hasLevel ? `${puzzle.name} - ${level}` : puzzle.name;

  // File name (e.g. "Icons_Sudoku_Easy.png")
  const iconFile = hasLevel
    ? `Icons_${puzzle.name}_${level}.png`
    : `Icons_${puzzle.name}.png`;

  // Normalize spaces to underscores
  const iconPath = `/images/game/${iconFile.replace(/\s+/g, '_')}`;

  return { puzzleName, iconPath };
}
