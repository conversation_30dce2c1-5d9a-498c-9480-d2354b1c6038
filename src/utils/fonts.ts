import { Poppins, Playfair_Display } from 'next/font/google';

const poppin_init = Poppins({
    subsets: ['latin'],
    weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
    variable: '--font-poppins',
});
const playfair_display_init = Playfair_Display({
    subsets: ['latin'],
    weight: ['400'],
    variable: '--font-playfair-display', // Correct to match your CSS
});

export const poppins = poppin_init.className;
export const playfair_display = playfair_display_init.className;