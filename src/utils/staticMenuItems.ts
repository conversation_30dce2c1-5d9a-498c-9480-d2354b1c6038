import { NavbarItemProps } from '@/types/navbar';

export const staticMenuItems: NavbarItemProps[] = [
  {
    id: 0,
    label: 'EXCLUSIVE DISCOUNTS',
    href: '/exclusive-discount',
    matcher: /^\/exclusive-discount\/(.*)/,
    sub: [
      {
        id: '01',
        label: 'All Discounts',
        href: '/exclusive-discount',
      },
      {
        id: '02',
        label: 'The Great Getaway',
        href: '/special-offers',
      },
      // {
      //   id: '03',
      //   label: 'Celebrity Cruise Flash Sale',
      //   href: '/promotion/celebrity-cruises-flash-sale',
      // },
    ],
  },
  {
    id: 1,
    label: 'DESTINATION GUIDES',
    href: '/destination',
    matcher: /^\/destination\/(.*)/,
    sub: [
      // { id: '11', label: 'DESTINATIONS GUIDES', href: '/destination' },
      {
        id: '12',
        label: 'Multi-continent Cruises',
        href: '/destination/multi-continent-cruises',
      },
      {
        id: '13',
        label: 'Antarctica Cruises',
        href: '/destination/antarctica-cruises',
      },
      {
        id: '14',
        label: 'Oceania Cruises',
        href: '/destination/oceania-cruises',
      },
      { id: '15', label: 'Asia Cruises', href: '/destination/asia-cruises' },
      {
        id: '16',
        label: 'Africa Cruises',
        href: '/destination/africa-cruises',
      },
      {
        id: '17',
        label: 'South America Cruises',
        href: '/destination/south-america-cruises',
      },
      {
        id: '18',
        label: 'North America Cruises',
        href: '/destination/north-america-cruises',
      },
      {
        id: '19',
        label: 'Northern Europe Cruises',
        href: '/destination/northern-europe-cruises',
      },
      {
        id: '110',
        label: 'Southern Europe Cruises',
        href: '/destination/southern-europe-cruises',
      },
    ],
  },
  {
    id: 2,
    label: 'CRUISE BY INTEREST',
    href: '/interest',
    matcher: /^\/interest\/(.*)/,
    sub: [
      // { id: '21', label: 'CRUISE BY INTEREST', href: '/interest' },
      { id: '22', label: 'History', href: '/interest/history' },
      { id: '23', label: 'Gastronomic', href: '/interest/gastronomic' },
      { id: '24', label: 'Arts & Crafts', href: '/interest/arts-and-crafts' },
      {
        id: '25',
        label: 'Health & Wellbeing',
        href: '/interest/health-and-wellbeing',
      },
      { id: '26', label: 'Family', href: '/interest/family' },
      {
        id: '27',
        label: 'Sport & Activity',
        href: '/interest/sport-and-activity',
      },
      {
        id: '28',
        label: 'Adventure & Exploration',
        href: '/interest/adventure-and-exploration',
      },
      {
        id: '29',
        label: 'Gardens & Architecture',
        href: '/interest/gardens-and-architecture',
      },
      {
        id: '1001',
        label: 'MUSIC & CULTURE',
        href: '/interest/music-and-culture',
      },
      {
        id: '1002',
        label: 'NATURE & WILDLIFE',
        href: '/interest/nature-and-wildlife',
      },
    ],
  },
  {
    id: 3,
    label: 'CRUISE LINES',
    href: '/cruise-line',
    matcher: /^\/cruise-line\/(.*)/,
    sub: [
      // { id: '31', label: 'CRUISE LINES', href: '/cruise-line' },
      {
        id: '32',
        label: 'Celebrity Cruises',
        href: '/cruise-line/celebrity-cruises',
      },
      { id: '34', label: 'AmaWaterways', href: '/cruise-line/ama-waterways' },
      {
        id: '35',
        label: 'Fred. Olsen Cruise Line',
        href: '/cruise-line/Fred-Olsen',
      },
      {
        id: '36',
        label: 'HX Hurtigruten Expedition',
        href: '/cruise-line/hx-hurtigruten-expeditions',
      },
      { id: '37', label: 'Hurtigruten', href: '/cruise-line/hurtigruten' },
      {
        id: '38',
        label: 'Ambassador Cruise Line',
        href: '/cruise-line/ambassador-Cruise-Line',
      },
      {
        id: '39',
        label: 'Regent Seven Seas Cruises',
        href: '/cruise-line/regent-seven-seas-cruises',
      },
      { id: '33', label: 'DFDS', href: '/cruise-line/dfds' },
    ],
  },

  {
    id: 4,
    label: 'New to cruising?',
    href: '/interest/new-to-cruising',
    matcher: /^\/new-to-cruising\/(.*)/,
  },

  {
    id: 5,
    label: 'ARTICLES',
    href: '/article',
    matcher: /^\/article\/(.*)/,
  },
];
