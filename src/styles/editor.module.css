/* Default styles for larger screens */

.editorContainer ul {
  list-style: inherit !important;
  margin: 0;
  padding: 37px;
}

.editorContainer a {
  color: #FF9A31;
  font-weight: 600;
  margin-left: 2px;
}

.editorContainer a:hover {
  text-decoration: underline;
}

.editorContainer ul li::marker {
  color: #ff9a31 !important;
}

.editorContainer p {
  padding-bottom: 20px;
}

.editorContainer figcaption {
  padding-top: 15px !important;
}

.editorContainer h1 {
  font-size: 32px !important;
  margin-bottom: 15px;
}

.editorContainer h3 {
  font-size: 12px;
  letter-spacing: 2.4px;
  margin-bottom: 15px;
}

.editorContainer h4 {
  font-size: 42px;
  margin-bottom: 15px;
}

.editorContainer h2 {
  font-size: 19px;
  letter-spacing: 2.4px;
  margin-bottom: 15px;
}

.editorContainer h5 {
  margin-top: 10px;
  margin-bottom: 10px;
  padding-top: 25px;
  padding-bottom: 25px;
  color: black;
  position: relative;
  font-size: 23px;
  margin-bottom: 15px;
}

.editorContainer h5::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 8%;
  /* Set the width to 10% */
  height: 2px;
  background-color: #ff9a31;
}

.editorContainer h6 {
  display: flex;
  /* font-family: 'Montserrat', sans-serif; */
  font-weight: 300;
  justify-content: center;
  font-size: 1.5em;
  margin-top: 10px;
  margin-bottom: 15px;
  padding-top: 25px;
  padding-bottom: 25px;
  color: black;
  line-height: 34px;
  position: relative;
}

.editorContainer h6::before,
.editorContainer h6::after {
  content: '';
  flex-grow: 1;
  border: 2px solid #ff9a31;
  width: 50%;
  /* Set the border width to 50% */
  position: absolute;
}

.editorContainer h6::before {
  top: 0;
}

.editorContainer h6::after {
  bottom: 0;
  text-align: center;
  /* Center text within h6 */
}
.editorContainer blockquote {
  padding: 50px 100px 50px 100px;
}

.editorContainer blockquote h1 {
  font-size: 28px;
  color: black;
  font-weight: 300;
}

.editorContainer blockquote h1::before {
  font-weight: bold;
  line-height: 1;
  position: relative;
  top: -3px;
}

.editorContainer blockquote h1::after {
  line-height: 1;
  position: relative;
  top: 0px;
  left: -1px;
}

.editorContainer blockquote h4 {
  color: var(--Primary-Orange, #ff9a31);
  /* font-family: Apercu; */
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.9px;
}

/* Responsive styles for mobile screens */
@media (max-width: 768px) {
  .editorContainer h1 {
    font-size: 1.8em;
    margin-top: 20px;
    padding-bottom: 10px;
  }

  .editorContainer h2,
  .editorContainer h5 {
    font-size: 1.5em;
    margin-top: 10px;
    margin-bottom: 10px;
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .editorContainer h6 {
    font-size: 20px;
    margin-top: 10px;
    margin-bottom: 10px;
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .editorContainer h3 {
    font-size: 18px;
  }

  .editorContainer h4 {
    font-size: 16px;
  }

  .editorContainer blockquote {
    padding: 30px;
    margin-top: 1%;
  }

  .editorContainer blockquote h1 {
    font-size: 24px;
  }
}
