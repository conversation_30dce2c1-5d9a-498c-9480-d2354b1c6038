@tailwind base;
@tailwind components;
@tailwind utilities;

.container {
  max-width: 1440px;
}

.Padding-Top {
  padding-top: 0rem !important;
}

.Toastify__progress-bar--success {
  background-color: #FF9A31 !important;
  /* Other styles for the success progress bar */
}

.swiper,
.swiper-initialized,
.swiper-horizontal,
.swiper-backface-hidden {
  height: 100% !important;
}

.logo-background-color {
  background: "rgba(255, 255, 255, 0.3)"
}

.editor-page>p {
  padding-bottom: 20px;
}

.my-10 {
  margin-bottom: 3.5rem !important;
}

.page-details-container {
  max-width: 1016px !important;
  margin: 0 auto;
}


.react-datepicker-wrapper {
  width: 100% !important;
}


.custom-select {
  position: relative;
  display: block;
  appearance: none;
  border: 1px solid #FF9A31;
  border-radius: 4px;
  padding: 10px;
  background-color: #fff;
  width: 100%;
}

.mt-12p {
  margin-top: 1rem !important;
}

.custom-select::after {
  content: '▼';
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  pointer-events: none;
}

/* Custom CSS */
.custom-list-marker li::marker {
  color: #FF9A31 !important;
}

.bg-orange-texture {
  background: #F5F2EE;
}

.landing-image {
  height: calc(80vh - 3.75rem);
  background: #eaeaea;
}

.ml-15p {
  margin-left: 15%;
}

.border-length-10 {
  border-bottom-width: 10%;
}

.bg-image-height-home {
  min-height: calc(95vh - 3.75rem);
}

.annotation-image-height {
  height: calc(70vh - 3.75rem);
}

.landing-caption {
  position: absolute;
  bottom: 40px;
  background-color: #f5f2ee;
  color: #000;
  max-width: 548px;
}

.callout-box {
  position: absolute;
  bottom: 40px;
  background-color: #f5f2ee;
  color: #000;
  max-width: 600px;
}

.bg-collective-image-url {
  background-image: url('../assets/svg/slider-bg-collective.svg');
  width: 100%;
  height: 100%;
}

.bg-dark-icon-offer {
  background-image: url('../assets/svg/slider-bg-collective.svg');
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: right;
}

.fixed-logo {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 50;
}

.destination-description p {
  font-size: 18px;
}

.article-slider p {
  font-size: 24px;
}

.hero-image {
  object-fit: cover;
  object-position: center;
}

.banner-image {
  width: 100%;
  background: #fff;
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.modal-box {
  background-color: #f5f2ee;
}

.swiper-pagination {
  position: relative !important;
  text-align: left !important;
  margin-top: 16px;
}

.swiper-pagination-bullet {
  width: 12px !important;
  height: 12px !important;
  background: #36453b !important;
  opacity: 100 !important;
}

.swiper-pagination-bullet-active {
  background: #FF9A31 !important;
}


.swiper-button-prev,
.swiper-button-next {
  width: 32px !important;
  height: 32px !important;
  background: rgb(255 255 255);
  border-radius: 999px;
  color: #36453b !important;
}


.hyperlink {
  color: #FF9A31;
}

.hyperlink:hover {
  color: #007bff;
  text-decoration: underline;
}


/* Custom arrow styling */
.custom-swiper-button-prev,
.custom-swiper-button-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 85px;
  border-radius: 50%;
  color: #36453b;
  cursor: pointer;
  z-index: 10;
}

.slider-img {
  position: relative;
  width: 85px;
  height: 29px;
  left: 4%;
}

.custom-swiper-button-prev {
  left: 75px;
}

.custom-swiper-button-next {
  right: 75px;
}


.rich-content-swiper-button-prev,
.rich-content-swiper-button-next {
  position: absolute;
  transform: translateY(-50%);
  cursor: pointer;
  z-index: 10;
  bottom: 0;
}

.rich-content-swiper-button-prev {
  left: 17px;
  bottom: 25px;
}

.rich-content-swiper-button-next {
  left: 75px;
  bottom: 25px;
}



/* Optional: Adjust z-index for Swiper navigation */
.swiper-button-prev,
.swiper-button-next {
  z-index: 9;
}

@keyframes multicolorBorderAnimation {
  0% {
    background-position: 0% 50%;
  }

  100% {
    background-position: 100% 50%;
  }
}

.multicolor-border-animation {
  background: linear-gradient(90deg, #ff8a00, #e52e71, #0075ff, #00ff00);
  background-size: 400% 400%;
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  animation: multicolorBorderAnimation 5s infinite;
}


.swiper-button-prev::after,
.swiper-button-next::after {
  font-size: 16px !important;
}

.card-continent:hover {
  background-color: rgba(255, 154, 49, 0.8) !important;
}

.card-continent:hover button {
  color: #000;
  border: 1px solid #e26f21;
  background: rgba(255, 154, 49, 0.8) !important;
}

.cruise-card-bg-img {
  background-image: url('../../public/images/cruise.png');
}

.basic-multi-select .css-13cymwt-control {
  border-color: #e26f21;
}


.video-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.border-cruise2 {
  border: 1px solid #DCDAD6;
}

.terms-and-condition-p p {
  margin-bottom: 20px;
  font-size: 14px;
}

.info-box-editor h3 {
  font-size: 24px;
  margin-bottom: 40px;
}

.info-box-editor p {
  font-size: 18px;
  line-height: 32.4px;
}

@media (min-width: 768px) {
  .page-details-container {
    width: 1016px;
  }

  .landing-image {
    height: calc(80vh - 5.25rem);
  }

  .swiper-button-prev,
  .swiper-button-next {
    width: 56px !important;
    height: 56px !important;
  }

  .swiper-button-prev::after,
  .swiper-button-next::after {
    font-size: 24px !important;
  }
}

/* Responsive adjustments */
@media only screen and (max-width: 767px) {

  .bg-image-height-home {
    height: calc(95vh - 3.75rem);
    min-height: calc(95vh - 3.75rem);
  }

  .custom-swiper-button-prev,
  .custom-swiper-button-next {
    width: 60px;
    height: 40px;
  }

  .slider-img {
    width: 60px;
  }

  .custom-swiper-button-prev {
    left: 4px;
  }

  .custom-swiper-button-next {
    right: 0px;
  }

  .landing-image {
    height: calc(40vh - 3.75rem);
    width: 100vw;
  }

  .banner-image {
    margin-top: 0;
  }

  .bg-image-height {
    height: calc(60vh - 3.75rem);
    min-height: calc(60vh - 3.75rem);
  }

  .annotation-image-height {
    height: calc(40vh - 3.75rem);
  }

  .page-details-container {
    width: 100%;
  }

  .destination-description p {
    font-size: 16px;
  }

  .info-box-editor h3 {
    font-size: 20px;
  }

  .info-box-editor p {
    font-size: 16px;
  }

  .article-slider p {
    font-size: 16px;
  }


}