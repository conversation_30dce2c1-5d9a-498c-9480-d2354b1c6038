/* @font-face {
  font-family: "ApercuRegular";
  src: url("../assets/font/ApercuRegular.otf") format("opentype");
}
@font-face {
  font-family: "ApercuMedium";
  src: url("../assets/font/ApercuMedium.otf") format("opentype");
}
@font-face {
  font-family: "ApercuBold";
  src: url("../assets/font/ApercuBold.otf") format("opentype");
}
@font-face {
  font-family: "ApercuProRegular";
  src: url("../assets/font/ApercuProRegular.otf") format("opentype");
}

@font-face {
  font-family: "ApercuProMedium";
  src: url("../assets/font/ApercuProMedium.otf") format("opentype");
} */

body {
  /* font-family: "adobe-garamond-pro", serif; */
  font-style: normal;
  line-height: normal;
  font-weight: 400;
  background-color: #F5F2EE;
  color:#000;
}

.ml-15p {
  margin-left: 15%;
}
.border-length-10 {
  border-bottom-width: 10%;
}


p {
  font-size: 18px;
}

h1 {
  /* font-family: "adobe-garamond-pro", serif; */
  font-family: var(--font-playfair-display);
  font-weight: 700;
  color: #37453b;
  margin-bottom:15px !important;
}

h2, h3 {
  font-family: var(--font-playfair-display);
  font-weight: 400;
}

h4,
h5 {
  /* font-family: "adobe-garamond-pro", serif; */
  font-weight: 400;
  margin-bottom:15px !important;
}

nav > ul > li > a {
  color: #000;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
}

.apercu_regular {
  /* font-family: "ApercuRegular"; */
  font-weight: 400;
}
.apercu_regular_pro {
  /* font-family: "ApercuProRegular"; */
  font-weight: 400;
}

.apercu_medium_pro {
  /* font-family: "ApercuProMedium"; */
  font-weight: 500;
}
.apercu_medium {
  /* font-family: "ApercuMedium"; */
  font-weight: 500;
}
.apercu_bold {
  /* font-family: "ApercuBold"; */
  font-weight: 700;
}

@media only screen and (max-width: 767px) {
  /* nav > ul > li > a {
    font-size: 13px;
  } */
}

