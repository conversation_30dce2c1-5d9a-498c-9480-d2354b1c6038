import { useState, useEffect } from "react";
// import { getCruiseLines } from "@/queries/cruise-line";

const useInfiniteScroll = (apiMethod, type = {} as any) => {
    const [cards, setCards] = useState<any>([]);
    const [pageIndex, setPageIndex] = useState(1);
    const pageSize = 25;
    const [hasMore, setHasMore] = useState(true);
    const [isLoading, setIsLoading] = useState(false)
    // const slug: any = type?.slug ? type?.slug : ''
    const [total, setTotal] = useState()

    const fetchMoreData = async (reset = false) => {
        try {
            const data = await apiMethod(reset ? 1 : pageIndex, pageSize, type);
            setCards(reset ? [...data.data] : [...cards, ...data.data]);
            setTotal(data?.meta?.pagination?.total)
            const updatedCardsLength = reset ? data?.data?.length : ((cards?.length ? cards?.length : 0) + data?.data?.length); // Calculate the updated length of cards

            if (updatedCardsLength === data?.meta?.pagination?.total) {
                setHasMore(false);
            } else {
                setPageIndex(reset ? 2 : (pageIndex + 1));
            }
        } catch (error) {
            setHasMore(false);
            console.error('Error :', error);
        }
    };

    useEffect(() => {
        const fetchData = async () => {
            setIsLoading(true);
            setPageIndex(1);
            setHasMore(true);
            try {
                await fetchMoreData(true);
                // Handle data after fetching (if needed)
            } catch (error) {
                // Handle errors if fetchMoreData() fails
                console.error('Error fetching data:', error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchData(); // Invoke the async function
    }, [type?.slug]);

    return { isLoading, total, cards, hasMore, fetchMoreData };
};

export default useInfiniteScroll;


