import { getOffers } from '@/queries/offers';
import { useEffect, useState } from 'react';


function useAllOffers() {
    const [offers, setOffers] = useState<any>([]);
    const [isOfferLoading, setIsOfferLoading] = useState<boolean>(false);

    useEffect(() => {
        const fetchData = async () => {
            let page = 1;
            let allOffers: any = [];

            let hasMoreData = true;
            setIsOfferLoading(true);
            while (hasMoreData) {
                const response = await getOffers(page, 25);
                if (response?.data?.length < 25) {
                    setIsOfferLoading(false)
                    hasMoreData = false;
                }
                const resData = response?.data;
                allOffers = [...allOffers, ...resData];
                setOffers(allOffers);
                page++;
            }
        };

        fetchData();
    }, []);

    return {
        isOfferLoading,
        offers
    };
}

export default useAllOffers;
