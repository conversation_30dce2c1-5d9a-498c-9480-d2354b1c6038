export interface SubItem {
  id: string | number;
  label: string;
  href?: string;
  sub?: SubItem[];
  onClickItem?: () => void;
  matcher?: RegExp;
}

export interface NavbarItemProps {
  id: string | number;
  label: string;
  href: string;
  matcher?: RegExp;
  sub?: SubItem[];
  isActive?: boolean;
  isTabletOrBelowDevice?: boolean;
  isMobileDrawer?: boolean;
  setIsDrawerOpen?: any;
  onClickItem?: () => void;
  currentMenu?: string | undefined | null;
  setCurrentMenu?: any;
  isOpenSubMenu?: boolean
}
