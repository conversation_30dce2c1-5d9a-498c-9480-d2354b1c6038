import { playfair_display } from '@/utils/fonts';
import React, { useEffect, useState } from 'react';
import PrimaryButton from './PrimaryButton';

const BlackFridayBanner = ({ data }) => {
  const [days, setDays] = useState<number>(0);
  const [hours, setHours] = useState<number>(0);
  const [mins, setMins] = useState<number>(0);
  const [secs, setSecs] = useState<number>(0);
  const [isFutureDate, setIsFutureDate] = useState<boolean>(false);
  const bgColor = data?.bg_color;
  const bgImage = data?.backgroundImage?.data?.attributes?.url;
  const iconImage = data?.icon_image?.data?.attributes?.url;

  // const [isPreviousDate, setIsPreviousDate] = useState<boolean>(false);

  const getTime = () => {
    const currentTime = Date.now();
    const countdownTime = Date.parse(data?.countdowndate);
    const time = countdownTime - currentTime;

    if (time > 0) {
      setIsFutureDate(true);
      setDays(Math.floor(time / (1000 * 60 * 60 * 24)));
      setHours(Math.floor((time / (1000 * 60 * 60)) % 24));
      setMins(Math.floor((time / 1000 / 60) % 60));
      setSecs(Math.floor((time / 1000) % 60));
    } else {
      setIsFutureDate(false);
      setDays(0);
      setHours(0);
      setMins(0);
      setSecs(0);
    }
  };

  useEffect(() => {
    const countdownTime = Date.parse(data?.countdowndate);
    const today = new Date().setHours(0, 0, 0, 0);
    // setIsPreviousDate(countdownTime < today);
    if (countdownTime > today) {
      const interval = setInterval(() => getTime(), 1000);
      return () => clearInterval(interval); // Properly clean up the interval
    }
  }, [data?.countdowndate]);

  // if (isPreviousDate) {
  //   return <p></p>;
  // }

  return (
    <div
      className="text-white text-center py-12 px-6 relative"
      style={{
        backgroundColor: bgColor,
        backgroundImage: `url('${bgImage}')`,
      }}
    >
      <div className="relative max-w-[800px] mx-auto">
        <div
          className="bg-center bg-no-repeat w-full h-[150px] flex items-center justify-center"
          style={{
            ...(iconImage ? { backgroundImage: `url('${iconImage}')` } : {}),
          }}
        >
          <h1
            className={`text-3xl font-bold mb-4 md:text-5xl text-white text-center ${playfair_display}`}
          >
            {isFutureDate ? data?.before_title : data?.after_title}
          </h1>
        </div>

        <p className={`text-base mb-6 mt-10 ${playfair_display}`}>
          {isFutureDate ? data?.before_excerpt : data?.after_excerpt}
        </p>
        <div>
          {isFutureDate ? (
            <div className={`flex gap-4 mt-8 justify-center`}>
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 text-2xl bg-brand rounded flex justify-center items-center">
                  {days}
                </div>
                <p className={`text-white text-base mt-1 ${playfair_display}`}>
                  Days
                </p>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 text-2xl bg-brand rounded flex justify-center items-center">
                  {hours}
                </div>
                <p className={`text-white text-base mt-1 ${playfair_display}`}>
                  Hours
                </p>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 text-2xl bg-brand rounded flex justify-center items-center">
                  {mins}
                </div>
                <p className={`text-white text-base mt-1 ${playfair_display}`}>
                  Minutes
                </p>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 text-2xl bg-brand rounded flex justify-center items-center">
                  {secs}
                </div>
                <p className={`text-white text-base mt-1 ${playfair_display}`}>
                  Seconds
                </p>
              </div>
            </div>
          ) : (
            <div className="flex justify-center mt-10">
              <PrimaryButton
                btnText="Claim Now"
                className="text-lg hover:bg-navy hover:text-cruise group/edit"
                textColor="text-black"
                href="/special-offers"
                iconColor="text-black group-hover/edit:text-cruise"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BlackFridayBanner;
