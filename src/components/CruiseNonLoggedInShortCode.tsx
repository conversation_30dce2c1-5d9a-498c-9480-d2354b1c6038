import React from 'react';

const CruiseNonLoggedInShortCode = (text, setOpenLoginModal) => {
  const components = text.split('{CRUISE LINE}');

  const replacedComponents = components.map((component, index) => {
    if (index === components.length - 1) {
      return (
        <div key={index} dangerouslySetInnerHTML={{ __html: component }} />
      );
    }

    return (
      <React.Fragment key={index}>
        <div key={index} dangerouslySetInnerHTML={{ __html: component }} />
        <div className="mb-2">
          <label
            onClick={() => setOpenLoginModal(true)}
            className="flex w-3/4 md:w-[500px] border-[#FF9A31] border-b-[2px] cursor-pointer  justify-center py-2 text-black tex-xl xl:text-[27px] hover:bg-orange "
            htmlFor="login_modal_id"
          >
            Log in to redeem this offer
          </label>
        </div>
      </React.Fragment>
    );
  });

  return <>{replacedComponents}</>;
};

export default CruiseNonLoggedInShortCode;
