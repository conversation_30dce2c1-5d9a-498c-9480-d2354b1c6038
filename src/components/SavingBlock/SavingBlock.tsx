import Stamp from '@/assets/svg/cc-stamp.svg';
import { Content } from './Content';
import { DiscountSlider } from './DiscountSlider';
import { useAppSelector } from '@/libs/hooks';
import { playfair_display } from '@/utils/fonts';

const SavingBlock = () => {
  const api = useAppSelector((state) => state.api.homeData);

  return (
    <div className="min-w-screen bg-orange py-10 md:py-[80px]">
      <div className="container mx-auto px-4 md:px-10">
        <div className="flex items-center justify-center">
          <Stamp />
        </div>
        {api?.cta && (
          <div
            className={`text-2xl md:text-[34px] text-black font-normal leading-normal md:text-center ${playfair_display}`}
          >
            {api?.cta?.heading}
          </div>
        )}
        {api?.cta?.cruise_lines && (
          <div className="w-full mt-4 md:mt-10">
            <DiscountSlider items={api?.cta.cruise_lines.data} />
          </div>
        )}
      </div>
      <div className="container mx-auto px-4 md:px-10">
        {api?.saving_block_promos && (
          <div className="mt-4 md:mt-10">
            <Content items={api.saving_block_promos} />
          </div>
        )}
      </div>
    </div>
  );
};

export default SavingBlock;
