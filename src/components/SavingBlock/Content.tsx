import Trust from '@/assets/svg/trust.svg';
import PlayfairTextSm from '../Typography/PlayfairTextSm';

const ContentItem = ({ text }) => {
  return (
    <div className="flex flex-col justify-center items-center px-10 py-3 md:py-10">
      <div className="w-[3.5rem] h-[3.5rem] rounded-full bg-dark-green flex justify-center items-center">
        <Trust />
      </div>
      <PlayfairTextSm text={text} className="text-center mt-4" />
    </div>
  );
};

export const Content = ({ items }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 justify-center gap-4 lg:gap-[80px] items-center bg-cruise">
      {items?.slice(0, 3)?.map((text, index) => (
        <ContentItem key={index} text={text.description} />
      ))}
    </div>
  );
};
