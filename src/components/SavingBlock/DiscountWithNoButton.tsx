import React from 'react';

const DiscountWithNoButton = () => {
  return (
    <div className="flex flex-col gap-4 justify-center items-center">
      <div
        className="relative text-center items-center justify-center flex flex-col gap-2 bg-no-repeat bg-center bg-cover"
        style={{
          backgroundImage: "url('/images/stamp-bg.png')",
          width: '168px',
          height: '168px',
        }}
      >
        <img
          src="/images/example-logo.png"
          alt="stamp"
          className="relative z-20 transform translate-y-[-20%]"
        />
        <p className="text-orange text-4xl font-semibold relative z-20 transform">
          20%
        </p>
        <p className="text-sm text-orange relative z-20 transform translate-y-[-40%]">
          MEMBERS DISCOUNT
        </p>
      </div>
    </div>
  );
};

export default DiscountWithNoButton;
