import { useAppDispatch, useAppSelector } from '@/libs/hooks';
import {
  setDiscountData,
  toggleDiscountModal,
  toggleLoginModal,
} from '@/libs/store/setting';
import { setSSOData } from '@/libs/store/ssoRedirect';
import { playfair_display } from '@/utils/fonts';
import { useEffect, useState } from 'react';

export const Discount = ({
  item,
  text = '',
  btnFontSize = 'text-base',
  width = '168px',
  height = '168px',
  showDiscountBtn = true,
}) => {
  const user = useAppSelector((state) => state.user);
  const dispatch = useAppDispatch();
  const setting = useAppSelector((state) => state.setting);
  const ssoRedirect = useAppSelector((state) => state.ssoRedirect);

  const handleClaimDiscount = () => {
    if (!item?.attributes?.calloutbox) return;

    dispatch(setDiscountData(item?.attributes));
    if (!user.loggedIn) {
      dispatch(toggleLoginModal());
      return;
    }
    dispatch(
      toggleDiscountModal({
        isModalOpen: true,
        data: item?.attributes,
      }),
    );
  };

  useEffect(() => {
    if (user.loggedIn && setting?.discountModal?.data?.calloutbox) {
      dispatch(
        toggleDiscountModal({
          isModalOpen: true,
          data: setting?.discountModal?.data,
        }),
      );
    } else if (user.loggedIn && ssoRedirect?.data?.calloutbox) {
      dispatch(
        toggleDiscountModal({ isModalOpen: true, data: ssoRedirect?.data }),
      );
      dispatch(setSSOData({ page: '/', data: {} }));
    }
  }, [user.loggedIn]);

  return (
    <div>
      <div className="flex md:flex-col  flex-row">
        <div className="flex flex-col justify-center lg:items-center">
          <div
            className="relative text-center items-center justify-between flex flex-col bg-no-repeat bg-center bg-cover p-3"
            style={{
              backgroundImage: "url('/images/stamp-bg.png')",
              width,
              height,
            }}
          >
            <div className="max-h-[60px] w-full">
              {item?.attributes?.logo?.data?.attributes?.url && (
                <img
                  src={item?.attributes?.logo?.data?.attributes?.url}
                  alt="stamp"
                  className="relative h-full z-20 w-full object-contain"
                />
              )}
            </div>

            <p className="text-orange text-3xl md:text-6xl font-semibold relative z-20 transform">
              {item?.attributes?.calloutbox?.saving}
            </p>
            <p className="text-sm text-orange relative z-20 transform translate-y-[-40%] mt-1 uppercase">
              {item?.attributes?.calloutbox?.discount_label ||
                'MEMBERS DISCOUNT'}
            </p>
          </div>
          {showDiscountBtn && (
            <button
              onClick={handleClaimDiscount}
              className={`bg-cruise py-2 px-2 ${btnFontSize} w-36 hover:bg-navy hover:text-cruise mt-4 uppercase`}
            >
              {item?.attributes?.calloutbox?.discount_button_label ||
                'CLAIM DISCOUNT'}
            </button>
          )}
        </div>
        {text && (
          <div
            className={`text-sm px-2 text-black font-normal ${playfair_display} leading-[21px] text-left lg:text-center mt-4`}
          >
            {text}
          </div>
        )}
      </div>
    </div>
  );
};
