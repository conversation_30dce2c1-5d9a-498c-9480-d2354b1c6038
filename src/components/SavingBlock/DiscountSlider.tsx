import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import { Autoplay, Navigation } from 'swiper';
import { Discount } from './Discount';

export const DiscountSlider = ({ items }: any) => {
  return (
    <Swiper
      modules={[Autoplay, Navigation]}
      breakpoints={{
        280: {
          slidesPerView: 1.3, // Change to 1 to prevent the first item from being cut off
          spaceBetween: 10,
        },
        380: {
          slidesPerView: 1.6, // Change to 1 to prevent the first item from being cut off
          spaceBetween: 10,
        },
        480: {
          slidesPerView: 2.5,
          spaceBetween: 16,
        },
        640: {
          slidesPerView: 3.3,
          spaceBetween: 10,
        },
        1000: {
          slidesPerView: 4,
          spaceBetween: 10,
        },
        1124: {
          slidesPerView: 5,
          spaceBetween: 10,
        },
        1314: {
          slidesPerView: 6,
          spaceBetween: 10,
        },
        1420: {
          slidesPerView: 7,
          spaceBetween: 10,
        },
      }}
    >
      {items.map((item, index) => {
        if (!item?.attributes?.calloutbox) return null;

        return (
          <SwiperSlide key={index}>
            <Discount item={item} btnFontSize="text-sm" />
          </SwiperSlide>
        );
      })}
    </Swiper>
  );
};
