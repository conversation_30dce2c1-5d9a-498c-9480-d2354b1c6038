import React from 'react';

const ReplaceShortCode = ({ attributes }) => {

  const {description = '', affiliate_link = '', affiliate_description = '' } = attributes;

  const components = description?.split('{CRUISE LINE}');

  const replacedComponents = components.map((component, index) => {

    if (index === components.length - 1) {
      return (
        <div key={index} dangerouslySetInnerHTML={{ __html: component }} />
      );
    }


    return (
      <React.Fragment key={index}>
        <div key={index} dangerouslySetInnerHTML={{ __html: component }} />
        <div>
           <div className='text-2xl mb-2'>REDEEMING YOUR OFFER</div>
           <div className='text-lg mb-3' dangerouslySetInnerHTML={{__html: affiliate_description}}></div>
           <a key={`button_${index}`} className="flex justify-center items-center  bg-orange w-[200px] h-[43px] text-white  text-xs	 apercu_medium_pro uppercase tracking-[2.4px] leading-4	 hover:underline hover:text-black" href={affiliate_link} target="_blank" rel="noopener noreferrer">
              REDEEM THE OFFER  &nbsp; <span>&gt;</span>
            </a>
        </div>
      </React.Fragment>
    );
  });

  return <>{replacedComponents}</>;
};

export default ReplaceShortCode;
