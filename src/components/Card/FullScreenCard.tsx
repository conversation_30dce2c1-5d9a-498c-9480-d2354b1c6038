import React from 'react';
import clsx from 'clsx';
import useIsMobile from '@/hooks/useIsMobile';
import { playfair_display } from '@/utils/fonts';
import PrimaryButton from '../PrimaryButton';
import Link from 'next/link';
import { useRouter } from 'next/router';

interface FullScreenCardProps {
  data: any; // Adjust this type based on your actual data structure
  index: number;
  page: string;
  onClick?: (data: any) => void;
  text: string;
  children?: React.ReactNode;
}

const FullScreenCard: React.FC<FullScreenCardProps> = ({
  data,
  index,
  page,
  onClick,
  text,
  children,
}) => {
  const isMobile = useIsMobile();
  const { attributes } = data;
  const { title, slug, excerpt } = attributes;
  const router = useRouter();

  const getImageUrl = () => {
    switch (page) {
      case 'destination':
        return attributes?.featured_image?.data?.attributes?.url || '';
      case 'interest':
      case 'cruise-line':
      case 'discount':
      case 'article':
        return attributes?.featured_image?.data?.[0]?.attributes?.url;
      default:
        return '';
    }
  };

  const imgUrl = getImageUrl();

  const handleButtonClick = (event) => {
    event.stopPropagation();
    onClick?.(attributes);
  };

  const handleCardClick = () => {
    if (page === 'discount') {
      router.push(`/exclusive-discount/${slug}`);
    }
  };

  const renderCardContent = () => (
    <div
      className={`flex w-full h-full bg-card-background-opacity group-hover:bg-card-hover ${
        page ? 'cursor-pointer' : ''
      }`}
      onClick={handleCardClick}
    >
      <div
        className={clsx('flex pb-10 p-4 lg:p-10 w-full', {
          'justify-start': isMobile || index % 2 !== 0,
          'justify-end': !isMobile && index % 2 === 0,
        })}
      >
        <div className="w-full lg:w-5/6">
          <div className="flex h-full flex-col justify-between">
            <div className="flex justify-end">{children}</div>
            <div>
              <h6 className="text-brand text-sm uppercase font-medium">
                {text}
              </h6>
              <h3
                className={`text-white text-[40px] lg:text-6xl mt-2 font-normal ${playfair_display}`}
              >
                {title}
              </h3>
              <div
                className={`line-clamp-5 mt-4 text-base lg:text-lg font-normal text-white destination-description ${playfair_display}`}
                dangerouslySetInnerHTML={{ __html: excerpt }}
              ></div>
              <div className="mt-5 pb-4 lg:pb-10">
                <PrimaryButton
                  textColor={page === 'discount' ? 'text-black' : 'text-white'}
                  btnText={
                    page === 'discount'
                      ? 'Get Discount Code'
                      : `Discover ${title}`
                  }
                  type={isMobile || page !== 'discount' ? 'outline' : 'primary'}
                  onClick={handleButtonClick}
                  className={
                    isMobile
                      ? 'text-white'
                      : page === 'discount'
                      ? 'text-sm hover:bg-navy hover:text-cruise group/edit z-20'
                      : 'text-sm hover:bg-brand hover:text-black group/edit hover:border hover:border-brand'
                  }
                  iconColor={
                    isMobile
                      ? 'text-brand'
                      : page === 'discount'
                      ? 'text-black group-hover/edit:text-cruise'
                      : 'text-brand group-hover/edit:text-black'
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div
      className="flex h-[600px] md:h-[h-500px] lg:h-[800px] bg-cover bg-center bg-no-repeat w-full group"
      style={{
        background: `url('${imgUrl}') lightgray 50% / cover no-repeat`,
      }}
    >
      {page === 'discount' ? (
        renderCardContent()
      ) : (
        <Link href={`/${page}/${slug}`} className="w-full">
          {renderCardContent()}
        </Link>
      )}
    </div>
  );
};

export default FullScreenCard;
