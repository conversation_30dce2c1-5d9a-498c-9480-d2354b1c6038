import { formatDate } from '@/utils';
import React from 'react';
import DarkCruiseCollectiveImg from '../DarkCruiseCollectiveImg';
import Link from 'next/link';
import { useAppSelector } from '@/libs/hooks';

const TravelPartnerCard = ({ card, setOpenLoginModal }) => {
  const imageUrl = card?.attributes?.featured_image?.data[0]?.attributes?.url;
  const title = card?.attributes?.title;
  const description = card?.attributes?.description;
  const logo = card?.attributes?.logo?.data?.attributes?.url;
  const offer = card?.attributes?.offer;
  const expiresdate = card?.attributes?.expires_date;
  const slug = card?.attributes?.slug;
  const formattedExpiryDate = formatDate(expiresdate);
  const session = useAppSelector((state) => state.user);
  
  return (
    <Link href={`/travel-partner/${slug}`}>
      <div className="grid grid-cols-1 md:grid-cols-2 mb-8">
        {/* Left Column with Background Image */}
        <div className="w-full relative">
          <div
            style={{
              backgroundImage: `url(${imageUrl})`,
            }}
            className="md:h-full h-[250px] bg-center bg-cover relative"
          >
            <div
              className="absolute top-0 px-3 md:px-7"
              style={{ background: 'rgba(255, 255, 255, 0.30)' }}
            >
              {/* Make the logo dynamic */}
              <img
                src={logo}
                alt=""
                width="150"
                height="150"
                className="mt-4" // Adjust the margin as needed
              />
            </div>
          </div>
        </div>

        {/* Right Column with Text Content */}
        <div className="bg-orange-texture p-3 md:p-7 w-full relative">
          <div className="max-w-[472px] text-3xl text-black py-2 mt-4 mb-5">
            {title}
          </div>

          <p
            dangerouslySetInnerHTML={{ __html: description }}
            className="line-clamp-3 text-black text-base"
          ></p>

          <div className="text-3xl mt-8">{offer}</div>
          <div className="button-section pt-7 pb-3">
            {!session?.loggedIn && (
              <>
                <button className="border-cruise border-[2px] text-black hover:bg-orange ">
                  <label
                    onClick={(e) => {
                      setOpenLoginModal(true);
                      e.stopPropagation();
                    }}
                    className="cursor-pointer text-xl block py-1.5 px-8"
                    htmlFor="login_modal_id"
                  >
                    Login to claim
                  </label>
                </button>
              </>
            )}
            {session?.loggedIn && (
              <button className="border text-xs apercu_medium_pro border-[#FF9A31] py-3 px-3 uppercase tracking-[3px] hover:bg-[#FF9A31] ">
                CLICK TO CLAIM OFFER
              </button>
            )}
            <div className="bottom-2 right-1.5 justify-end absolute">
              <DarkCruiseCollectiveImg />
            </div>
          </div>

          <div className="px-2 py-1 font-semibold text-xs tracking-[1.54px] apercu_medium uppercase">
            EXPIRES {formattedExpiryDate}
          </div>
        </div>
      </div>
    </Link>
  );
};

export default TravelPartnerCard;
