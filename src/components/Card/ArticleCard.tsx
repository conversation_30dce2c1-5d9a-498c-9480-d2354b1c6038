import Link from 'next/link';
import CardImage from './CardImage';
import { useRouter } from 'next/router';
import Image from 'next/image';
import ArrowColorIcon from '@/assets/svg/arrow-right-color.svg';
import { playfair_display } from '@/utils/fonts';
import clsx from 'clsx';

const ArticleCard = ({ article, page, className = '' }) => {
  const { attributes } = article;
  const { title, slug, featured_image, author_info, excerpt, partnership } =
    attributes;

  const showPartnership = () => {
    if (!partnership?.is_partnership) return null;

    const text = partnership?.organisation || 'Paid Partnership';

    return (
      <div className="absolute mt-[-41px] md:mt-[-65px] left-[11px] bg-brand text-white text-xs font-semibold px-3 py-1">
        {text}
      </div>
    );
  };

  const featuredImage = featured_image?.data?.length
    ? featured_image?.data[0]?.attributes.url
    : '/images/default.jpg';

  return (
    <div className={`group card ${className} relative`}>
      <Link href={`/${page}/${slug}`}>
        <div
          className="flex h-[300px] sm:h-[600px] md:h-[390px] lg:h-[400px] xl:h-[500px] relative bg-cover bg-center bg-no-repeat w-full"
          style={{
            background: `url('${featuredImage}') lightgray 50% / cover no-repeat`,
          }}
        >
          <div className="flex items-end w-full group-hover:bg-card-hover">
            <div
              className={clsx(
                'px-4 py-4 md:py-10 bg-navy opacity-95 z-20 w-full m-2.5 border-b-8 border-navy group-hover:border-brand',
              )}
            >
              {showPartnership()}
              <div
                className={`text-xl md:text-2xl font-normal text-white text-left ${playfair_display}`}
              >
                {title}
              </div>
              <div className="flex justify-between gap-4 mt-3">
                <div className="flex gap-3">
                  {/* {author_info?.image?.data?.attributes?.url && (
                    <Image
                      src={author_info?.image?.data?.attributes?.url}
                      alt={author_info?.image?.data?.attributes?.name}
                      className="w-6 h-6 rounded-full"
                      width={24}
                      height={24}
                    />
                  )}

                  <h2
                    className={`text-sm font-normal leading-6 text-white text-left ${playfair_display}`}
                  >
                    {author_info?.name}
                  </h2> */}
                  <p
                    className={`text-sm text-left font-normal text-white ${playfair_display}`}
                  >
                    {(excerpt || '').replace(/<[^>]*>?/gm, '').split(' ')
                      .length > 14
                      ? `${(excerpt || '')
                          .replace(/<[^>]*>?/gm, '')
                          .split(' ')
                          .slice(0, 14)
                          .join(' ')}...`
                      : (excerpt || '').replace(/<[^>]*>?/gm, '')}
                  </p>
                </div>
                <div className="text-sm">
                  <ArrowColorIcon />
                </div>
              </div>
            </div>
          </div>
          {page === 'travel-partner' && (
            <div className="absolute right-0 top-2.5 px-3 flex">
              <div
                className="bg-no-repeat bg-center bg-cover flex items-center justify-center"
                style={{
                  backgroundImage: "url('/images/stamp-bg.png')",
                  width: '130px',
                  height: '130px',
                }}
              >
                {attributes?.logo?.data?.attributes?.url && (
                  <div className="px-3">
                    <img
                      className="w-20 md:w-36 logo-background-color"
                      src={attributes?.logo?.data?.attributes?.url}
                      alt={attributes?.logo?.data?.attributes?.name}
                    />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </Link>
    </div>
  );
};

export default ArticleCard;