import { useRouter } from 'next/router';
import ArrowColorIcon from '@/assets/svg/arrow-right-color.svg';
import { playfair_display } from '@/utils/fonts';
import clsx from 'clsx';
import ArrowButton from '../Shared/ArrowButton';

const OfferCard2 = ({ data, className = '' }) => {
  const { attributes } = data;

  const { title, slug, featured_image, excerpt } = attributes;

  const featuredImage = featured_image?.data?.length
    ? featured_image?.data[0]?.attributes.url
    : '/images/default.jpg';

  const router = useRouter();

  const onClick = (e) => {
    e.preventDefault();
    router.push(`/exclusive-discount/${slug}`);
  };

  const excerptText =
    excerpt.replace(/<[^>]*>?/gm, '').split(' ').length > 10
      ? `${excerpt
          .replace(/<[^>]*>?/gm, '')
          .split(' ')
          .slice(0, 10)
          .join(' ')}...`
      : excerpt.replace(/<[^>]*>?/gm, '');

  return (
    <div className={`group card ${className} relative`}>
      <div onClick={onClick} className="cursor-pointer">
        <div
          className="flex relative w-full aspect-square max-w-[600px] mx-auto bg-cover bg-center bg-no-repeat"
          style={{
            background: `url('${featuredImage}') 50% / cover no-repeat`,
          }}
        >
          <div className="flex items-end w-full group-hover:bg-card-hover">
            <div
              className={clsx(
                'px-3 py-4 md:py-1 bg-navy opacity-95 z-20 w-full m-2.5 border-b-8 border-navy group-hover:border-brand',
              )}
            >
              <div
                className={`text-xl md:text-xl font-normal text-white text-left ${playfair_display}`}
              >
                {title}
              </div>
              <div className="flex justify-between gap-4 mt-3">
                <div className="flex gap-3 flex-col w-full">
                  <p
                    className={`text-sm text-left font-normal text-white ${playfair_display}`}
                  >
                    {excerptText}
                  </p>

                  <button
                    className={clsx(
                      `w-full flex flex-nowrap items-center justify-center px-2 py-1 font-medium group-hover/edit bg-cruise text-black text-sm hover:bg-navy hover:text-cruise group`,
                    )}
                  >
                    Redeem offer
                    <span className="text-black ml-2">
                      <ArrowButton />
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OfferCard2;
