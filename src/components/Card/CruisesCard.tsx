import { useSession } from "next-auth/react";
import Link from "next/link";

const CruisesCard = ({ cruise, setOpenLoginModal }) => {
  const { data: session } = useSession();
  const { attributes } = cruise;
  const { title, slug, excerpt, featured_image, logo } = attributes;

  const featuredImage = featured_image?.data?.length ? featured_image?.data[0]?.attributes?.url : "";
  const logoImg = logo?.data?.attributes?.url;
  const offer = attributes?.offer;



  return (

    <Link href={`/cruise-line/${slug}`}>
    <div className="grid grid-cols-1 md:grid-cols-2 mb-8">

      <div className="w-full relative">
        <div
          style={{
            backgroundImage: `url(${featuredImage})`,
          }}
          className="md:h-full h-[250px] bg-center bg-cover relative"
        >
          <div
            className="absolute top-0 px-3 md:px-7"
            style={{ background: "rgba(255, 255, 255, 0.30)" }}
          >
            <img
              src={logoImg}
              alt=""
              width="150"
              height="150"
              className="mt-4"
            />
          </div>
        </div>
      </div>
      <div className="border-[11px] border-cruise p-3 md:p-5 w-full relative">
        <div className="max-w-[472px] text-3xl text-black py-2 mt-4 mb-5">
          {title}
        </div>

        <p
          dangerouslySetInnerHTML={{ __html:  excerpt }}
          className="line-clamp-3 text-black text-base"
        ></p>

        <div className="text-3xl mt-8 font-bold min-h-[40px]">{offer ? (offer + ' OFF') : ''} </div>
        
        <div className="button-section pt-7 pb-3">
          {!session?.user?.email ? (
            <>
                <label
                  onClick={(e) => {
                    setOpenLoginModal(true);
                    e.stopPropagation();
                  }}
                  className="flex w-full xl:w-[230px] text-xs lg:text-base bg-orange justify-center items-center h-[50px] cursor-pointer py-1.5  apercu_medium_pro uppercase tracking-[2.4px] hover:underline"
                  htmlFor="login_modal_id"
                >
                  LOGIN TO CLAIM
                </label>
            </>
          ) : (           
            <button className="border text-xs text apercu_medium_pro border-[#FF9A31] py-3 px-2 uppercase tracking-[3px] hover:bg-[#FF9A31] ">
              CLICK TO CLAIM OFFER
            </button>)
        }
  
        </div>
      </div>
    </div>
    </Link>
  );
};

export default CruisesCard;
