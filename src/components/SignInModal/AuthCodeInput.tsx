import React from 'react';
import PrimaryButton from '../PrimaryButton';

const AuthCodeInput = ({ code, setCode, handleVerifyCode, loading, error }) => (
  <>
    <div className="mb-4">
      <input
        type="text"
        placeholder="Enter your authentication code"
        value={code}
        onChange={(e) => setCode(e.target.value)}
        className="w-full text-lg p-3 border border-[#DCDAD6] focus:outline-none focus:border-black"
        autoComplete="off"
      />
    </div>
    {error && <div className="text-red-500 text-sm mb-4">{error}</div>}
    <PrimaryButton
      btnText={loading ? 'LOADING...' : 'VERIFY CODE'}
      className="text-lg hover:bg-navy hover:text-cruise group/edit"
      textColor="text-black"
      onClick={handleVerifyCode}
      disabled={loading}
      iconColor="text-black group-hover/edit:text-cruise"
    />
  </>
);

export default AuthCodeInput;
