import React from 'react';
import { playfair_display } from '@/utils/fonts';

const SignInHeader = ({ step, onClose }) => (
  <div className="flex flex-col md:flex-row justify-between mb-10">
    <h2
      className={`text-2xl md:text-[34px] ${playfair_display} text-center md:text-left font-normal order-1 md:order-0`}
    >
      {step === 'initial'
        ? 'Sign in or register'
        : step === 'register'
        ? 'Register with Cruise Collective'
        : 'Login'}
    </h2>
    <button
      className="text-gray-500 hover:text-gray-700 text-3xl hover:text-red order-0 md:order-1"
      onClick={onClose}
    >
      &times;
    </button>
  </div>
);

export default SignInHeader;
