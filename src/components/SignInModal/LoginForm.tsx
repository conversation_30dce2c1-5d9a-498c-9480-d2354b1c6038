import React from 'react';
import PrimaryButton from '../PrimaryButton';
import { playfair_display } from '@/utils/fonts';

const LoginForm = ({
  email,
  password,
  setPassword,
  handleLogin,
  setStep,
  loading,
  error,
  handleEditEmail,
}) => (
  <>
    <div className={`text-lg mb-4`}>
      <span className="mr-3 text-black">{email}</span> |
      <span
        className="text-sm text-orange cursor-pointer ml-3"
        onClick={handleEditEmail}
      >
        edit
      </span>
    </div>
    <div className="mb-4">
      <input
        type="password"
        placeholder="Password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        className="w-full text-lg p-3 border border-[#DCDAD6] focus:outline-none focus:border-black"
        autoComplete="off"
      />
    </div>
    {error && <div className="text-red-500 text-sm mb-4">{error}</div>}
    <div
      className={`mb-4 text-sm text-black ${playfair_display} leading-[25.2px] font-normal`}
    >
      <a href="#" onClick={() => setStep('forgot')} className="underline">
        Forgotten password?
      </a>
    </div>
    <div className="flex justify-start w-full mt-4 md:mt-10">
      <PrimaryButton
        btnText={loading ? 'LOADING...' : 'LOG IN'}
        className="text-lg hover:bg-navy hover:text-cruise group/edit"
        textColor="text-black"
        onClick={handleLogin}
        disabled={loading}
        iconColor="text-black group-hover/edit:text-cruise"
      />
    </div>
  </>
);

export default LoginForm;
