import React from 'react';
import PrimaryButton from '../PrimaryButton';
import Link from 'next/link';
import { playfair_display } from '@/utils/fonts';

const RegisterForm = ({
  email,
  name,
  surname,
  password,
  setName,
  setSurname,
  handlePasswordChange,
  handleRegister,
  handleNewsletter,
  handlePrivacyPolicy,
  loading,
  error,
  handleEditEmail,
}) => (
  <>
    <span className={`${playfair_display}`}>
      <div className={`text-lg mb-4`}>
        <span className="mr-3 text-black">{email}</span> |
        <span
          className="text-sm text-orange cursor-pointer ml-3"
          onClick={handleEditEmail}
        >
          edit
        </span>
      </div>
      <div className="mb-4 flex">
        <input
          type="text"
          placeholder="First Name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          className="w-1/2 text-lg p-3 border border-[#DCDAD6] focus:outline-none focus:border-black mr-2 md:mr-4"
          autoComplete="off"
        />
        <input
          type="text"
          placeholder="Surname (optional)"
          value={surname}
          onChange={(e) => setSurname(e.target.value)}
          className="w-1/2 text-lg p-3 border border-[#DCDAD6] focus:outline-none focus:border-black"
          autoComplete="off"
        />
      </div>
      <div className="mb-4">
        <input
          type="password"
          placeholder="Create Password"
          value={password}
          onChange={handlePasswordChange}
          className="w-full text-lg p-3 border border-[#DCDAD6] focus:outline-none focus:border-black"
          autoComplete="off"
        />
      </div>

      {/* Newsletter form */}
      <div className="mb-4">
        <label className="flex items-start">
          <input
            className="mt-1"
            type="checkbox"
            name="newsletter"
            onChange={handleNewsletter}
          />
          <span className="ml-3 text-xs text-black font-normal leading-[18px]">
            <strong>I would like Cruise Collective</strong> (operated by{' '}
            <strong>Our Media Limited</strong>) to send me updates, exclusive
            offers, and promotions via email. You can unsubscribe at any time.
            For more details on how we handle your personal information, please
            refer to our{' '}
            <Link
              target="_blank"
              href="/privacy-policy"
              className="underline text-black font-bold"
            >
              Privacy Policy
            </Link>
          </span>
        </label>
      </div>
      <div className="mb-4">
        <label className="flex items-start">
          <input
            className="mt-1"
            type="checkbox"
            name="privacypolicy"
            onChange={handlePrivacyPolicy}
          />
          <span className="ml-3 text-xs text-black font-normal leading-[18px]">
            By signing up, you agree to our{' '}
            <Link
              target="_blank"
              href="/privacy-policy"
              className="underline text-black font-bold"
            >
              Privacy Policy
            </Link>{' '}
            and{' '}
            <Link
              target="_blank"
              href="/terms-and-conditions"
              className="underline text-black font-bold"
            >
              Terms & conditions
            </Link>
          </span>
        </label>
      </div>

      {error && <div className="text-red text-sm mb-4"><pre>{error}</pre></div>}
    </span>
    <div className="flex justify-start w-full mt-4 md:mt-12">
      <PrimaryButton
        btnText={loading ? 'LOADING...' : 'COMPLETE REGISTRATION'}
        className="text-lg hover:bg-navy hover:text-cruise group/edit"
        textColor="text-black"
        onClick={handleRegister}
        disabled={loading}
        iconColor="text-black group-hover/edit:text-cruise"
      />
    </div>
  </>
);

export default RegisterForm;
