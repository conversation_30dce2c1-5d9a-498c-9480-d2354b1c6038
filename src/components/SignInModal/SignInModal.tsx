import React, { useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/libs/hooks';
import { toggleLoginModal } from '@/libs/store/setting';
import { signIn, useSession } from 'next-auth/react';
import { loginUser } from '@/libs/store/user';
import { userExist } from '@/queries/zephr';
import { showToast } from '@/utils';
import { postRegister } from '@/queries';
import SignInHeader from './SignInHeader';
import SocialLoginButtons from './SocialLoginButtons';
import EmailInput from './EmailInput';
import RegisterForm from './RegisterForm';
import LoginForm from './LoginForm';
import Footer from './Footer';
import ForgotPassword from './ForgotPassword';

const data = [
  {
    id: 1,
    text: 'Claim discounts instantly and redeem at point of purchase with our cruise partners',
  },
  {
    id: 2,
    text: 'Completely free to join',
  },
  {
    id: 3,
    text: 'Get exclusive deals and offers',
  },
];

const SignInModal = () => {
  const [step, setStep] = useState('initial');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [surname, setSurname] = useState('');
  const [newsletter, setNewsletter] = useState(false);
  const [privacyPolicy, setPrivacyPolicy] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const setting = useAppSelector((state) => state.setting);
  const dispatch = useAppDispatch();
  const { data: session } = useSession();

  if (!setting.loginModal) return null;

  const onClose = () => {
    dispatch(toggleLoginModal());
    setStep('initial');
  };

  const handleNewsletter = () => setNewsletter(!newsletter);
  const handlePrivacyPolicy = () => setPrivacyPolicy(!privacyPolicy);

  const handleEmailSubmit = async () => {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(email)) {
      setError('Please enter a valid email address.');
      return;
    }

    setError('');
    setLoading(true);
    const userExists = await userExist(email);
    setLoading(false);

    if (userExists) {
      setStep('login');
    } else {
      setStep('register');
    }
  };

  const handleLogin = async () => {
    setLoading(true);
    const result = await signIn('credentials', {
      redirect: false,
      email,
      password,
    });
    setLoading(false);

    if (result?.error) {
      showToast('Invalid password or email, please try again.', 'error');
    } else {
      dispatch(loginUser(session?.user));
      showToast('Logged in successfully.', 'success');
      onClose();
    }
  };

  const handleRegister = async () => {
    if (!name.trim()) {
      setError('First name is required.');
      return;
    }

    if (password.length < 8) {
      setError(
        'Password must be at least 8 characters long.',
      );
      return;
    }

    if (!privacyPolicy) {
      setError('You must accept our privacy policy to continue with registration');
      return;
    }

    setLoading(true);
    const register = await postRegister({
      email,
      firstname: name,
      lastname: surname || '',
      password,
      newsletter,
    });
    setLoading(false);

    if (register) {
      showToast('Registered successfully.', 'success');
      dispatch(loginUser(register.data));
      await signIn('credentials', { redirect: false, email, password });
      onClose();
    }
  };

  const handlePasswordChange = (e) => {
    const value = e.target.value;
    setPassword(value);
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-navy bg-opacity-70"
      onClick={onClose}
    >
      <div
        className="bg-white px-4 md:px-10 py-10 rounded-none md:rounded-lg shadow-lg w-full h-full md:h-auto md:max-w-[936px] relative"
        onClick={(e) => e.stopPropagation()}
      >
        <SignInHeader step={step} onClose={onClose} />
        <div className="flex flex-col md:flex-row h-[85%] md:h-auto gap-4 md:gap-10 justify-center md:justify-start">
          <div className="md:w-2/3 flex flex-col">
            {step === 'forgot' && <ForgotPassword email={email} onClose={onClose} />}
            {step === 'initial' && (
              <>
                <SocialLoginButtons />
                <EmailInput
                  email={email}
                  setEmail={setEmail}
                  handleEmailSubmit={handleEmailSubmit}
                  loading={loading}
                  error={error}
                />
              </>
            )}
            {step === 'register' && (
              <RegisterForm
                email={email}
                name={name}
                surname={surname}
                password={password}
                setName={setName}
                setSurname={setSurname}
                handlePasswordChange={handlePasswordChange}
                handleRegister={handleRegister}
                handleNewsletter={handleNewsletter}
                handlePrivacyPolicy={handlePrivacyPolicy}
                loading={loading}
                error={error}
                handleEditEmail={() => setStep('initial')}
              />
            )}
            {step === 'login' && (
              <LoginForm
                email={email}
                password={password}
                setPassword={setPassword}
                handleLogin={handleLogin}
                setStep={setStep}
                loading={loading}
                error={error}
                handleEditEmail={() => setStep('initial')}
              />
            )}
          </div>
          <Footer data={data} />
        </div>
      </div>
    </div>
  );
};

export default SignInModal;
