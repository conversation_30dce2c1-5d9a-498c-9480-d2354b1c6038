import React from 'react';
import FacebookIcon from '@/assets/svg/social-icons/facebook-ico.svg';
import GoogleIcon from '@/assets/svg/social-icons/google-ico.svg';
import { baseUrl } from '@/utils';
import { useAppDispatch, useAppSelector } from '@/libs/hooks';
import { useRouter } from 'next/router';
import { setSSOData } from '@/libs/store/ssoRedirect';

const SocialLoginButtons = () => {
  const router = useRouter();
  const setting = useAppSelector((state) => state.setting);
  const dispatch = useAppDispatch();

  const handleLogin = (provider) => {
    dispatch(
      setSSOData({ page: router.asPath, data: setting?.discountModal?.data }),
    );
    window.location.href = baseUrl + '/api/connect/' + provider;
  };

  return (
    <>
      <div className="mb-2">
        <button
          onClick={() => handleLogin('facebook')}
          className="w-full text-sm flex items-center justify-center bg-gray-100 text-black py-4 px-4 hover:bg-gray-200 bg-[#EFEFEF]"
        >
          <FacebookIcon className="w-6 h-6 mr-3" />
          CONTINUE WITH FACEBOOK
        </button>
      </div>
      <div className="mb-4">
        <button
          onClick={() => handleLogin('google')}
          className="w-full text-sm flex items-center justify-center bg-gray-100 text-black py-4 px-4 hover:bg-gray-200 bg-[#EFEFEF]"
        >
          <GoogleIcon className="w-6 h-6 mr-3" />
          CONTINUE WITH GOOGLE
        </button>
      </div>
    </>
  );
};

export default SocialLoginButtons;
