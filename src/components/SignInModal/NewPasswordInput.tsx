import React from 'react';
import PrimaryButton from '../PrimaryButton';

const NewPasswordInput = ({
  password,
  confirmPassword,
  setPassword,
  setConfirmPassword,
  handleResetPassword,
  loading,
  error,
}) => (
  <>
    <div className="mb-4">
      <input
        type="password"
        placeholder="Enter new password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        className="w-full text-lg p-3 border border-[#DCDAD6] focus:outline-none focus:border-black"
        autoComplete="off"
      />
    </div>
    <div className="mb-4">
      <input
        type="password"
        placeholder="Confirm new password"
        value={confirmPassword}
        onChange={(e) => setConfirmPassword(e.target.value)}
        className="w-full text-lg p-3 border border-[#DCDAD6] focus:outline-none focus:border-black"
        autoComplete="off"
      />
    </div>
    {error && <div className="text-red-500 text-sm mb-4">{error}</div>}
    <PrimaryButton
      btnText={loading ? 'LOADING...' : 'RESET PASSWORD'}
      className="text-lg hover:bg-navy hover:text-cruise group/edit"
      textColor="text-black"
      onClick={handleResetPassword}
      disabled={loading}
      iconColor="text-black group-hover/edit:text-cruise"
    />
  </>
);

export default NewPasswordInput;
