import React, { useEffect, useState } from 'react';
import AuthCodeInput from './AuthCodeInput';
import NewPasswordInput from './NewPasswordInput';
import { showToast } from '@/utils'; // Assuming this utility is available
import EmailInput2 from './EmailInput2';
import {
  requestResetCode,
  resetPassword,
  verifyAuthCode,
} from '@/queries/zephr';

interface Props {
  email: string;
  onClose: () => void;
}

const ForgotPassword = ({ email, onClose }: Props) => {
  const [step, setStep] = useState('email'); // 'email', 'authCode', or 'newPassword'
  const [code, setCode] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (password) {
      setError(
        password.length >= 8
          ? ''
          : 'Password must be at least 8 characters long',
      );
    }
  }, [password]);

  const handleRequestCode = async () => {
    if (!email) {
      setError('Please enter your email.');
      return;
    }
    setError('');
    setLoading(true);

    // Call API to request reset code
    try {
      await requestResetCode(email);
      showToast('Reset code sent to your email.', 'success');
      setStep('authCode');
    } catch (err) {
      setError('Error requesting reset code. Please try again.');
    }
    setLoading(false);
  };

  const handleVerifyCode = async () => {
    if (!code) {
      setError('Please enter the authentication code.');
      return;
    }
    setError('');
    setLoading(true);

    // Call API to verify code
    try {
      await verifyAuthCode(email, code);
      showToast('Code verified successfully.', 'success');
      setStep('newPassword');
    } catch (err) {
      setError('Invalid authentication code. Please try again.');
    }
    setLoading(false);
  };

  const handleResetPassword = async () => {
    if (!password || !confirmPassword) {
      setError('Please fill out both password fields.');
      return;
    }
    if (password !== confirmPassword) {
      setError('Passwords do not match.');
      return;
    }
    setError('');
    setLoading(true);

    // Call API to reset password
    try {
      await resetPassword(email, code, password);
      showToast('Password reset successfully.', 'success');
      onClose();
      // You can redirect the user to the login page here
    } catch (err) {
      setError('Error resetting password. Please try again.');
    }
    setLoading(false);
  };

  return (
    <>
      {' '}
      {step === 'email' && (
        <EmailInput2
          email={email}
          handleRequestCode={handleRequestCode}
          loading={loading}
          error={error}
        />
      )}
      {step === 'authCode' && (
        <AuthCodeInput
          code={code}
          setCode={setCode}
          handleVerifyCode={handleVerifyCode}
          loading={loading}
          error={error}
        />
      )}
      {step === 'newPassword' && (
        <NewPasswordInput
          password={password}
          confirmPassword={confirmPassword}
          setPassword={setPassword}
          setConfirmPassword={setConfirmPassword}
          handleResetPassword={handleResetPassword}
          loading={loading}
          error={error}
        />
      )}
    </>
  );
};

export default ForgotPassword;
