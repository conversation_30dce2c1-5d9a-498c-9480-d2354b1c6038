import React from 'react';
import PrimaryButton from '../PrimaryButton';
import { playfair_display } from '@/utils/fonts';

const EmailInput = ({ email, setEmail, handleEmailSubmit, loading, error }) => (
  <>
    <div className="mb-4">
      <input
        type="email"
        placeholder="Email address"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        className={`w-full text-lg p-3 border border-[#DCDAD6] focus:outline-none focus:border-black ${playfair_display}`}
        autoComplete="off"
      />
    </div>
    {error && <div className={`text-red-500 text-sm mb-4 ${playfair_display}`}>{error}</div>}
    <div className="flex justify-start w-full">
      <PrimaryButton
        btnText={loading ? 'LOADING...' : 'CONTINUE'}
        className="text-lg hover:bg-navy hover:text-cruise group/edit"
        textColor="text-black"
        onClick={handleEmailSubmit}
        disabled={loading}
        iconColor="text-black group-hover/edit:text-cruise"
      />
    </div>
  </>
);

export default EmailInput;
