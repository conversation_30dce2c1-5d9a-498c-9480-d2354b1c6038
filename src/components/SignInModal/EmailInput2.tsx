import React from 'react';
import PrimaryButton from '../PrimaryButton';

const EmailInput2 = ({ email, handleRequestCode, loading, error }) => (
  <>
    <div className="mb-4">
      <input
        type="email"
        disabled
        placeholder="Enter your email"
        value={email}
        className="w-full text-lg p-3 border border-[#DCDAD6] focus:outline-none focus:border-black"
        autoComplete="off"
      />
    </div>
    {error && <div className="text-red-500 text-sm mb-4">{error}</div>}
    <PrimaryButton
      btnText={loading ? 'LOADING...' : 'REQUEST RESET CODE'}
      className="text-lg hover:bg-navy hover:text-cruise"
      textColor="text-black"
      onClick={handleRequestCode}
      disabled={loading}
    />
  </>
);

export default EmailInput2;
