import { playfair_display } from '@/utils/fonts';
import React, { useEffect, useRef, useState } from 'react';
import { ParallaxBanner } from 'react-scroll-parallax';
import RightIcon from '@/assets/svg/right-icon.svg';
import HomeCCIcon from '@/assets/svg/home-cc-icon.svg';
import useIsMobile from '@/hooks/useIsMobile';
import Player from '@vimeo/player';
import { formatDate } from '@/utils';
interface Video {
  url?: string;
  provider?: string;
  providerUid?: string;
}

interface ParallaxBoxProps {
  image: string;
  title: string;
  headline: string;
  button?: any;
  video?: Video;
  isHomePage: boolean;
  promotion?: boolean;
  departure_date?: string;
}

const ParallaxBox: React.FC<ParallaxBoxProps> = ({
  image,
  title,
  headline,
  button,
  video,
  isHomePage = false,
  promotion,
  departure_date = null,
}) => {
  const [videoLoaded, setVideoLoaded] = useState(false);
  const [videoError, setVideoError] = useState(false);
  const [thumbnailUrl, setThumbnailUrl] = useState('');
  const isMobile = useIsMobile();
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const handleVideoError = () => setVideoError(true);

  const videoUrl =
    video?.provider === 'vimeo'
      ? `https://player.vimeo.com/video/${video?.providerUid}?autoplay=1&loop=1&muted=1&background=1`
      : video?.url;

  const shouldShowImage = isMobile || !videoUrl || videoError;

  const getVimeoThumbnail = async (videoId) => {
    try {
      const response = await fetch(
        `https://vimeo.com/api/v2/video/${videoId}.json`,
      );
      const data = await response.json();
      return data[0]?.thumbnail_large;
    } catch (err) {
      console.error('Error fetching Vimeo thumbnail', err);
    }
  };

  useEffect(() => {
    const fetchThumbnailAndInitPlayer = async () => {
      if (video?.provider === 'vimeo') {
        const thumbnail = await getVimeoThumbnail(video.providerUid!);
        setThumbnailUrl(thumbnail);
      }

      // Handle Vimeo events using the Vimeo Player API
      if (iframeRef.current && video?.provider === 'vimeo') {
        const player = new Player(iframeRef.current);

        // Listen for when the video starts playing
        player.on('play', () => {
          setVideoLoaded(true);
        });

        return () => {
          player.off('play');
          // player.off('error');
        };
      }
    };

    if (videoUrl) {
      fetchThumbnailAndInitPlayer();
    }
  }, [videoUrl, video?.providerUid, video?.provider]);

  return (
    <div className="relative w-full md:px-10">
      {/* Image section */}
      {shouldShowImage && (
        <ParallaxBanner
          className=" h-[90vh] md:h-auto"
          style={{ aspectRatio: '2 / 1' }}
          layers={[
            {
              image,
              speed: -20,
            },
            {
              children: (
                <div
                  className="absolute inset-0"
                  style={{
                    background:
                      'linear-gradient(0deg, rgba(0, 0, 0, 0.40) 0%, rgba(0, 0, 0, 0.40) 100%)',
                  }}
                />
              ),
            },
          ]}
        />
      )}

      {/* Video section */}
      {videoUrl && !isMobile && !videoError && (
        <div className="relative" style={{ padding: '56.25% 0 0 0' }}>
          <iframe
            className={`absolute top-0 left-0 w-full h-full transition-opacity duration-500 ${
              video?.provider === 'vimeo' ? 'px-0' : 'px-10'
            }`}
            src={videoUrl}
            allow="autoplay; fullscreen"
            allowFullScreen
            title={title}
            style={{
              pointerEvents: 'none',
              border: 'none',
            }}
            loading="lazy"
            ref={iframeRef} // Attach the ref to the iframe
            onError={handleVideoError}
          ></iframe>

          {/* 40% Overlay on video */}
          <div
            className="absolute inset-0 transition-colors duration-500"
            style={{
              background: videoLoaded
                ? 'linear-gradient(0deg, rgba(0, 0, 0, 0.40) 0%, rgba(0, 0, 0, 0.40) 100%)'
                : `linear-gradient(0deg, rgba(0, 0, 0, 0.40) 0%, rgba(0, 0, 0, 0.40) 100%), url(${thumbnailUrl}) lightgray 50% / cover no-repeat`,
              zIndex: 1,
            }}
          ></div>
        </div>
      )}

      {isHomePage && (
        <div className="absolute inset-0 z-10 flex items-center justify-start md:justify-center">
          <div className="w-full px-2 md:px-0 md:max-w-[700px] lg:max-w-[1000px] xl:max-w-[1080px]">
            <div className="flex flex-col text-white items-center text-center">
              <HomeCCIcon className="block md:hidden lg:block" />
              <HomeCCIcon
                width="120"
                height="160"
                viewBox="0 0 202 228"
                className="hidden md:block lg:hidden"
              />

              <div
                className={`text-5xl md:text-4xl lg:text-5xl xl:text-6xl font-normal text-white text-left md:text-center leading-[52.8px] md:leading-[66px] ${playfair_display} my-4 lg:my-10 px-4`}
              >
                {title}
              </div>
              <p className="text-lg md:text-xl xl:text-2xl font-medium leading-[19.8px] md:leading-[26.4px] uppercase text-white text-left md:text-center w-full px-4">
                {headline}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Overlay Content: Title and Headline */}
      {promotion && (
        <div className="">
          <div className="absolute inset-0 z-10 flex items-center justify-start md:justify-center">
            <div className="w-full px-2 md:px-0 md:max-w-[700px] xl:max-w-[750px] bg-black-transparent-rgba py-10">
              <div className="flex flex-col text-white items-center text-center">
                <div
                  className={`text-3xl md:text-xl lg:text-3xl xl:text-4xl font-normal text-white text-left md:text-center leading-[52.8px] md:leading-[66px] ${playfair_display} my-4 lg:my-10 px-4`}
                >
                  {title}
                </div>
                {departure_date && (
                  <div className="text-lg md:text-xl xl:text-2xl font-medium leading-[19.8px] md:leading-[26.4px] text-white text-left md:text-center w-full px-4">
                    DEPARTURE DATE: {formatDate(departure_date, 'long')}
                  </div>
                )}
                <a
                  className="bg-brand  mt-4 text-lg md:text-xl xl:text-2xl font-medium leading-[19.8px] md:leading-[26.4px] uppercase text-white text-left md:text-center p-2"
                  href={button?.url}
                >
                  {button?.name}
                </a>
              </div>
            </div>
          </div>
        </div>
      )}

      {!isHomePage && !promotion && (
        <div className="absolute inset-0 z-10 flex items-center justify-start md:justify-center">
          <div className="p-4 md:p-6 text-center w-full md:max-w-[700px] lg:max-w-[970px] xl:max-w-[1080px]">
            <div className="min-w-[120px] flex md:justify-center">
              <RightIcon />
            </div>
            <h3 className="text-lg md:text-2xl text-brand mt-4 text-left md:text-center">
              {title}
            </h3>
            <h1
              className={`text-5xl md:text-6xl mt-5 text-white font-bold text-left md:text-center leading-[52.8px] md:leading-[66px] ${playfair_display}`}
            >
              {headline}
            </h1>
          </div>
        </div>
      )}
    </div>
  );
};

export default ParallaxBox;
