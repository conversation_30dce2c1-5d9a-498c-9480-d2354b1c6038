import { playfair_display } from '@/utils/fonts';
import Image from 'next/image';
import React from 'react';

const AuthorBlock = ({ attributes }) => {
  return (
    <div className="w-full md:w-3/4 mx-auto px-2 md:px-[80px]">
      <div className="w-full flex flex-col md:flex-row justify-between gap-4 pt-10 ">
        <div className="flex gap-3 w-full md:w-1/4">
          {attributes?.author_info?.image?.data?.attributes?.url && (
            <div>
              <Image
                src={attributes?.author_info?.image?.data?.attributes?.url}
                alt="Author image"
                className="w-8 h-8 rounded-full hidden md:block"
                width={32}
                height={32}
              />
              <Image
                src={attributes?.author_info?.image?.data?.attributes?.url}
                alt="Author image"
                className="w-6 h-6 rounded-full block md:hidden"
                width={24}
                height={24}
              />
            </div>
          )}

          <div className={`${playfair_display} font-normal text-black text-sm`}>
            {attributes?.author_info?.name}
          </div>
        </div>
        <div
          className={`${playfair_display} font-normal text-black w-full md:w-3/4 text-sm`}
        >
          {attributes?.author_info?.description}
        </div>
      </div>
    </div>
  );
};

export default AuthorBlock;
