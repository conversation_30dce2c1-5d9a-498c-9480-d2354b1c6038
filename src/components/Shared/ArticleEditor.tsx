import { playfair_display } from '@/utils/fonts';
import styles from '../../styles/article.module.css';
import RenderArticleEditor from './RenderArticleEditor';

const ArticleEditor = ({
  editorText,
  gallery,
  annotatedImage,
  splitImages,
  articleCta,
}) => {
  return (
    <div className="pt-10 md:pt-[80px] w-full md:w-3/4 mx-auto">
      <div
        className={`${styles.articleContainer} px-4 lg:px-10 xl:px-[80px] ${playfair_display}`}
      >
        {RenderArticleEditor(
          editorText,
          '{gallery}',
          gallery,
          '{annoted_image}',
          annotatedImage[0],
          '{split_image}',
          splitImages,
          '{article_cta}',
          articleCta,
        )}
      </div>
    </div>
  );
};

export default ArticleEditor;
