import React from 'react';
import BodyImageSlider from '@/containers/atoms/BodyImageSlider';
import { mapDynamicGallery } from '@/utils';
import AnnotatedImage from './AnnotatedImage';
import ArticleCTA from '../CTABanner/ArticleCTA';

const RenderArticleEditor = (
  text,
  type,
  galleryData,
  type2,
  type2Data,
  type3,
  type3Data,
  type4,
  type4Data,
) => {
  // Splitting the text by the gallery tag type
  const components = splitText(text, type);

  const replacedComponents = components.map((component, index) => (
    <React.Fragment key={index}>
      {Annotad(component, type2, type2Data, type3, type3Data, type4, type4Data)}
      {index < components.length - 1 && (
        <BodyImageSlider
          key={`slider_${index}`}
          sliderItems={mapDynamicGallery(galleryData?.[0]?.images?.data)}
        />
      )}
    </React.Fragment>
  ));

  return <>{replacedComponents}</>;
};

export default RenderArticleEditor;

// Helper function to split text by a specified type
const splitText = (text, type) => text?.split(type);

const Annotad = (
  text,
  type2,
  type2Data,
  type3,
  type3Data,
  type4,
  type4Data,
) => {
  const components = splitText(text, type2);

  return components.map((component, index) => (
    <React.Fragment key={index}>
      {SplitImage(component, type3Data, type4Data)}
      {index < components.length - 1 && (
        <AnnotatedImage
          src={type2Data?.image?.data?.attributes?.url}
          alt={type2Data?.image?.data?.attributes?.name}
          caption={
            type2Data?.image?.data?.attributes?.caption
              ? type2Data?.image?.data?.attributes?.caption
              : type2Data?.name
          }
          className="h-[300px] md:h-[600px]"
        />
      )}
    </React.Fragment>
  ));
};

const SplitImage = (text, type3Data, type4Data) => {
  const components =
    text?.split(/(\{split_image(?:_(\d+))?\}|\{article_cta\})/) || [];

  let dynamicIndex = 1;

  return components
    .map((component, index) => {
      if (
        !component ||
        typeof component !== 'string' ||
        /^\d+$/.test(component.trim())
      ) {
        return null;
      }

      if (component === '{article_cta}') {
        return <ArticleCTA key={index} data={type4Data} />;
      }

      const match = component.match(/\{split_image(?:_(\d+))?\}/);
      let splitIndex = 0;

      if (match) {
        const tagIndex = match[1] ? parseInt(match[1], 10) : null;
        if (tagIndex !== null) {
          if (tagIndex <= 1) {
            splitIndex = 0;
          } else {
            splitIndex = dynamicIndex;
            dynamicIndex++;
          }
        }

        const splitData = type3Data?.[splitIndex] || type3Data?.[0];

        return (
          <React.Fragment key={index}>
            <div className="mx-auto grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <AnnotatedImage
                  src={splitData?.first_image?.data?.attributes?.url}
                  alt={splitData?.first_image?.data?.attributes?.name}
                  caption={splitData?.first_image?.data?.attributes?.caption}
                  className="h-[300px] md:h-[500px]"
                />
              </div>
              <div>
                <AnnotatedImage
                  src={splitData?.second_image?.data?.attributes?.url}
                  alt={splitData?.second_image?.data?.attributes?.name}
                  caption={splitData?.second_image?.data?.attributes?.caption}
                  className="h-[300px] md:h-[500px]"
                />
              </div>
            </div>
          </React.Fragment>
        );
      }

      return component.trim() ? (
        <div key={index} dangerouslySetInnerHTML={{ __html: component }} />
      ) : null;
    })
    .filter(Boolean);
};
