import RightIcon from '@/assets/svg/right-icon.svg';
import { playfair_display } from '@/utils/fonts';

const PageHeader = ({ data: { subTitle, title } }) => {
  return (
    <div className="my-[80px]">
      <div className="w-full lg:max-w-[1016px] lg:mx-auto">
        <div className="flex lg:justify-center">
          <RightIcon />
        </div>
        <h3 className="text-lg sm:tex-xl lg:text-2xl font-medium leading[19.8px] lg:leading-[26.4px] uppercase mt-4 text-brand text-left lg:text-center">
          {subTitle}
        </h3>
        <h1
          className={`text-5xl lg:text-6xl font-normal text-black leading-[52.8px] lg:leading-[66px] tracking-[-1.44px] lg:tracking-[-1.8px] mt-4 lg:mt-10 lg:text-center mb-0 ${playfair_display}`}
        >
          {title}
        </h1>
      </div>
    </div>
  );
};

export default PageHeader;
