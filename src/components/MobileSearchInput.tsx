import React from 'react';
import SearchIcon from './SearchIcon';

const MobileSearchInput = () => {
  const [search, setSearch] = React.useState('');

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    window.location.href = `/search?query=${search}`;
  };

  return (
    <form
      className="flex w-full h-full justify-center items-center  md:hidden"
      onSubmit={handleSubmit}
    >
      <div className="flex relative w-full h-full">
        <input
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          type="text"
          className="w-full transition-all duration-300 ease-in-out pl-5 pr-10 bg-transparent outline-0 rounded p-3 border "
          placeholder="Search"
        />
        <button
          type="submit"
          className="absolute right-0 top-0 h-full flex items-center pr-5"
        >
          <SearchIcon />
        </button>
      </div>
    </form>
  );
};

export default MobileSearchInput;
