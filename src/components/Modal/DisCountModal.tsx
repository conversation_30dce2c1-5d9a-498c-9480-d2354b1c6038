import { useAppDispatch, useAppSelector } from '@/libs/hooks';
import { toggleDiscountModal } from '@/libs/store/setting';
import { playfair_display } from '@/utils/fonts';
import UseDiscountInfo from '../Promo/UseDiscountInfo';
import PrimaryButton from '../PrimaryButton';
import { useState } from 'react';
import Link from 'next/link';
import ArrowButton from '../Shared/ArrowButton';
import clsx from 'clsx';

const DisCountModal = () => {
  const dispatch = useAppDispatch();
  const setting = useAppSelector((state) => state.setting);
  const [isShowHowWorks, setHowItWorks] = useState<boolean>(false);
  const [buttonText, setButtonText] = useState('Copy to clipboard');
  if (!setting?.discountModal?.isModalOpen) return null;

  const onClose = () => {
    dispatch(
      toggleDiscountModal({
        isModalOpen: false,
        data: {},
      }),
    );
  };

  const handleClickOutside = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const toggleHowDoesItWork = () => {
    setHowItWorks((prev) => !prev);
  };

  const handleButtonClick = () => {
    navigator.clipboard.writeText(description).then(() => {
      setButtonText('Copied!');
      setTimeout(() => {
        setButtonText('Copy to clipboard');
      }, 1200);
    });
  };

  const { calloutbox = {}, logo } = setting?.discountModal?.data;
  const {
    saving = '',
    sub_heading = '',
    title = '',
    description = '',
    button_text = '',
    button_url = '',
    terms_and_condition: terms,
    discount_label = '',
    discount_button_label = '',
  } = calloutbox;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-navy bg-opacity-70"
      onClick={handleClickOutside}
    >
      <div
        className="bg-white px-4 md:px-10 py-10 rounded-none md:rounded-lg shadow-lg w-full h-full md:h-auto md:max-w-[936px] relative"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex flex-col md:flex-row justify-between">
          <h2
            className={`text-2xl md:text-[34px] ${playfair_display} text-center md:text-left font-normal order-1 md:order-0`}
          >
            Your Cruise Collective Discount
          </h2>
          <button
            className="text-gray-500 hover:text-gray-700 text-3xl hover:text-red order-0 md:order-1"
            onClick={onClose}
          >
            &times;
          </button>
        </div>

        <div
          className="mt-10 flex flex-col lg:flex-row gap-6 lg:gap-2.5 w-full"
        >
          <div className="flex flex-col gap-4 justify-center items-center">
            <div
              className="relative text-center items-center justify-center flex-col gap-2 bg-no-repeat bg-center bg-cover hidden md:flex"
              style={{
                backgroundImage: "url('/images/stamp-bg.png')",
                width: '195px',
                height: '100%',
              }}
            >
              {logo?.data?.attributes?.url && (
                <img
                  src={logo?.data?.attributes?.url}
                  alt="stamp"
                  className="relative z-20 transform translate-y-[-20%] px-4"
                />
              )}
              <p
                className={`text-orange text-[52px] font-bold relative z-20 transform`}
              >
                {saving}
              </p>
              <p className="text-xs text-orange relative z-20 transform translate-y-[-40%] uppercase">
                {discount_label || 'MEMBERS DISCOUNT'}
              </p>
            </div>

            <div className="flex gap-4 w-full md:hidden items-center">
              <div className="w-3/5 md:w-full">
                {logo?.data?.attributes?.url && (
                  <img
                    src={logo?.data?.attributes?.url}
                    alt="stamp"
                    className="relative z-20 transform translate-y-[-20%] px-4"
                  />
                )}
              </div>
              <div className="w-2/5 md:w-full">
                <p
                  className={`text-brand text-[40px] text-center font-bold normal-case`}
                >
                  {saving}
                </p>
                <p className="text-xs text-orange text-center uppercase">
                  {discount_label || 'MEMBERS DISCOUNT'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-orange p-3 w-full border border-dashed border-paper">
            <div className="p-6 border-cruise border h-full">
              {!isShowHowWorks && (
                <>
                  <h3
                    className={`md:text-2xl ${playfair_display} text-center font-normal text-black text-xl`}
                  >
                    {title}
                  </h3>
                  <div className="flex justify-center items-center flex-col md:flex-row gap-4 mt-2">
                    <div className="text-4xl md:text-[52px] text-white font-bold">
                      {description ?? 'none'}
                    </div>
                    <PrimaryButton
                      className="text-sm hover:bg-navy hover:text-cruise min-w-[175px]"
                      textColor="text-black"
                      btnText={buttonText}
                      isArrowShow={false}
                      onClick={handleButtonClick}
                    />
                  </div>
                  <p className="uppercase text-center text-xs text-black font-medium mt-2">
                    {sub_heading}
                  </p>
                </>
              )}

              {!!isShowHowWorks &&
                calloutbox?.how_to_use?.map((item, idx) => (
                  <div key={idx} className="mb-4">
                    <div className="text-sm mb-1 font-medium text-black uppercase">
                      {item?.title}
                    </div>
                    <p
                      className={`text-sm text-left font-normal text-black ${playfair_display}`}
                    >
                      {item?.description}
                    </p>
                  </div>
                ))}

              <div className="flex md:hidden mt-12 justify-center">
                <PrimaryButton
                  className="text-sm"
                  textColor="text-black"
                  onClick={toggleHowDoesItWork}
                  btnText={isShowHowWorks ? 'Back to code' : 'How it works'}
                  isArrowShow={false}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="mt-10 mb-10 hidden md:block">
          <div
            className={`font-normal text-black text-2xl ${playfair_display}`}
          >
            How to use your discount
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 justify-center mt-10 gap-4 md:gap-10 pb-10 border-b">
            {calloutbox?.how_to_use?.map((item, idx) => (
              <UseDiscountInfo key={idx} data={item} />
            ))}
          </div>
        </div>

        <div className="mt-12 md:mt-4 flex flex-col md:flex-row gap-3 pb-8">
          <div
            className={`text-sm md:w-1/2 w-full ${playfair_display} font-normal`}
            style={{ color: 'rgba(0, 0, 0, 0.60)' }}
          >
            For full details and terms of use, see the vendors
            <Link
              target="_blank"
              className="ml-1 underline text-black"
              onClick={onClose}
              href={`/terms-and-conditions/${terms?.data?.attributes?.slug}`}
            >
              terms and conditions
            </Link>
          </div>
          <div className="md:w-1/2 w-full md:flex justify-end">
            <Link target="_blank" href={button_url}>
              <button
                className={clsx(
                  `uppercase flex flex-nowrap items-center justify-center px-4 py-3 font-medium group-hover/edit bg-cruise text-black text-sm hover:bg-navy hover:text-cruise group`,
                )}
              >
                {button_text}
                <span className="text-black group-hover:text-cruise ml-1.5">
                  <ArrowButton />
                </span>
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DisCountModal;
