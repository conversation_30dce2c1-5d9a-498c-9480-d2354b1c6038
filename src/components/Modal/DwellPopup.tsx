import { useAppDispatch, useAppSelector } from '@/libs/hooks';
import { loginUser } from '@/libs/store/user';
import { postRegister } from '@/queries';
import { userExist } from '@/queries/zephr';
import { showToast } from '@/utils';
import { signIn, useSession } from 'next-auth/react';
import React, { useState } from 'react';
import SignInHeader from '../SignInModal/SignInHeader';
import ForgotPassword from '../SignInModal/ForgotPassword';
import SocialLoginButtons from '../SignInModal/SocialLoginButtons';
import EmailInput from '../SignInModal/EmailInput';
import RegisterForm from '../SignInModal/RegisterForm';
import LoginForm from '../SignInModal/LoginForm';
import Footer from '../SignInModal/Footer';
import { toggleDWellModal } from '@/libs/store/setting';
import { playfair_display } from '@/utils/fonts';
const data = [
  // {
  //   id: 1,
  //   text: "Don't forget to sign up and get your discounts immediately!",
  // },
  {
    id: 2,
    text: "It's totally free to join the Collective and you'll also receive exclusive deals and offers from the best cruise brands which match your passions.",
  },
  // {
  //   id: 3,
  //   text: 'Continue with Facebook',
  // },
  // {
  //   id: 4,
  //   text: 'Continue with Google',
  // },
];

const DwellPopup = () => {
  const [step, setStep] = useState('initial');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [surname, setSurname] = useState('');
  const [newsletter, setNewsletter] = useState(false);
  const [privacyPolicy, setPrivacyPolicy] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const setting = useAppSelector((state) => state.setting);
  const dispatch = useAppDispatch();
  const { data: session } = useSession();

  if (!setting?.dwellModal?.isModalOpen) return null;

  const onClose = () => {
    dispatch(
      toggleDWellModal({
        data: {},
      }),
    );
  };

  const handleNewsletter = () => setNewsletter(!newsletter);
  const handlePrivacyPolicy = () => setPrivacyPolicy(!privacyPolicy);

  const handleEmailSubmit = async () => {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(email)) {
      setError('Please enter a valid email address.');
      return;
    }

    setError('');
    setLoading(true);
    const userExists = await userExist(email);
    setLoading(false);

    if (userExists) {
      setStep('login');
    } else {
      setStep('register');
    }
  };

  const handleLogin = async () => {
    setLoading(true);
    const result = await signIn('credentials', {
      redirect: false,
      email,
      password,
    });
    setLoading(false);

    if (result?.error) {
      showToast('Invalid password or email, please try again.', 'error');
    } else {
      dispatch(loginUser(session?.user));
      showToast('Logged in successfully.', 'success');
      onClose();
    }
  };

  const handleRegister = async () => {
    if (!name.trim()) {
      setError('First name is required.');
      return;
    }

    const passwordPattern =
      /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>_\-])[A-Za-z\d!@#$%^&*(),.?":{}|<>_\-]{8,}$/;
    if (!passwordPattern.test(password)) {
      setError(
        'Password must be at least 8 characters, including 1 uppercase letter, 1 number, and 1 special character.',
      );
      return;
    }

    if (!privacyPolicy) {
      setError(
        'You must accept our privacy policy do continue with registration',
      );
      return;
    }

    setLoading(true);
    const register = await postRegister({
      email,
      firstname: name,
      lastname: surname || '',
      password,
      newsletter,
    });
    setLoading(false);

    if (register) {
      showToast('Registered successfully.', 'success');
      dispatch(loginUser(register.data));
      await signIn('credentials', { redirect: false, email, password });
      onClose();
    }
  };

  const handlePasswordChange = (e) => {
    setPassword(e.target.value);
    if (step === 'register') {
      const passwordPattern =
        /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>_\-])[A-Za-z\d!@#$%^&*(),.?":{}|<>_\-]{8,}$/;
      setError(
        passwordPattern.test(e.target.value) ? '' : 'Password must be valid.',
      );
    } else {
      setError('');
    }
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-navy bg-opacity-70"
      onClick={onClose}
    >
      <div
        className="bg-white px-4 md:px-10 py-10 rounded-none md:rounded-lg shadow-lg w-full h-full md:h-auto md:max-w-[936px] relative"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex flex-col md:flex-row justify-between mb-10 md:items-start">
          <h2
            className={`text-2xl md:text-[34px] lg:w-3/4 ${playfair_display} text-center md:text-left font-normal order-1 md:order-0 leading-normal`}
          >
            {step === 'initial'
              ? `Don't forget to sign up and get your discounts immediately!`
              : step === 'register'
              ? 'Register with Cruise Collective'
              : 'Login'}
          </h2>
          <button
            className="text-gray-500 hover:text-gray-700 text-3xl hover:text-red order-0 md:order-1"
            onClick={onClose}
          >
            &times;
          </button>
        </div>

        <div className="flex flex-col md:flex-row h-[85%] md:h-auto gap-4 md:gap-10 justify-center md:justify-start">
          <div className="md:w-2/3 flex flex-col">
          {step === 'forgot' && <ForgotPassword email={email} onClose={onClose} />}
            {step === 'initial' && (
              <>
                <SocialLoginButtons />
                <EmailInput
                  email={email}
                  setEmail={setEmail}
                  handleEmailSubmit={handleEmailSubmit}
                  loading={loading}
                  error={error}
                />
              </>
            )}
            {step === 'register' && (
              <RegisterForm
                email={email}
                name={name}
                surname={surname}
                password={password}
                setName={setName}
                setSurname={setSurname}
                handlePasswordChange={handlePasswordChange}
                handleRegister={handleRegister}
                handleNewsletter={handleNewsletter}
                handlePrivacyPolicy={handlePrivacyPolicy}
                loading={loading}
                error={error}
                handleEditEmail={() => setStep('initial')}
              />
            )}
            {step === 'login' && (
              <LoginForm
                email={email}
                password={password}
                setPassword={setPassword}
                handleLogin={handleLogin}
                setStep={setStep}
                loading={loading}
                error={error}
                handleEditEmail={() => setStep('initial')}
              />
            )}
          </div>
          <Footer data={data} />
        </div>
      </div>
    </div>
  );
};

export default DwellPopup;
