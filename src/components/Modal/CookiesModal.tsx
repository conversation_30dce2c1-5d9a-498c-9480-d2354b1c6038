import { useEffect, useState } from 'react';
import { getCookiePopups } from '@/queries';
import { playfair_display } from '@/utils/fonts';
import PrimaryButton from '../PrimaryButton';
import ArrowButton from '../Shared/ArrowButton';

interface ICookiePopup {
  attributes: {
    title: string;
    description: string;
    buttons: { label: string }[];
  };
}

const CookiesModal = () => {
  const [cookiePopups, setCookiePopups] = useState<ICookiePopup[]>([]);
  const [showCookiesModal, setShowCookiesModal] = useState(true);

  useEffect(() => {
    const isAcceptedCookie = localStorage.getItem('acceptedCookies');
    if (!isAcceptedCookie) {
      fetchCookiePopups();
    }
  }, []);

  const fetchCookiePopups = async () => {
    try {
      const { data } = await getCookiePopups();
      setCookiePopups(data);
    } catch (error) {
      console.error('Error fetching cookie popups:', error);
    }
  };

  const handleAcceptCookies = () => {
    localStorage.setItem('acceptedCookies', 'true');
    setShowCookiesModal(false);
  };

  return (
    <>
      {showCookiesModal && cookiePopups?.length > 0 && (
        <div className="fixed bottom-0 z-50 left-0 right-0 bg-orange p-4 ">
          <div className="pb-4 text-base uppercase text-black">
            {cookiePopups[0]?.attributes?.title}
          </div>
          <div>
            <span className={`${playfair_display}`}>
              {cookiePopups[0]?.attributes?.description}
            </span>
            <button
              onClick={handleAcceptCookies}
              className={`bg-cruise py-3 uppercase text-black text-base px-12 relative ml-2.5 group hover:bg-navy hover:text-cruise`}
            >
              <span>
                {cookiePopups[0]?.attributes.buttons[0]?.label}
              </span>

              <span className="absolute right-4 top-4  group-hover/edit:text-cruise">
                {' '}
                <ArrowButton />{' '}
              </span>
            </button>

            <a
              href="/privacy-policy" // Replace with the actual link to your privacy policy
              className={`bg-gray-500 hover:bg-gray-700 text-black py-2 px-4 rounded hover:underline ${playfair_display}`}
              target="_blank" // Open in a new tab
              rel="noopener noreferrer"
            >
              Privacy Policy
            </a>
          </div>
        </div>
      )}
    </>
  );
};

export default CookiesModal;
