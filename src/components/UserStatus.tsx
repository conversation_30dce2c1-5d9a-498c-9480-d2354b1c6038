import { signOut } from 'next-auth/react';
import { useState } from 'react';
import SuccessfulModal from './Modal/SuccessfulModal';
import { successModalDto } from '@/Interface/Dto';
import { useRouter } from 'next/router';
import { showToast } from '@/utils';
import PrimaryButton from './PrimaryButton';
import { toggleLoginModal } from '@/libs/store/setting';
import { useAppDispatch } from '@/libs/hooks';
import { playfair_display } from '@/utils/fonts';
import { resetUser } from '@/libs/store/user';
import { useSelector } from 'react-redux';
import { RootState } from '@/libs/store/store';

const UserStatus = ({ setIsDrawerOpen }) => {
  const [showSuccessModal, setShowSuccessModal] = useState<successModalDto>({});
  const dispatch = useAppDispatch();
  const router = useRouter();
  const session = useSelector((state: RootState) => state.user);

  const handleLogout = async () => {
    try {
      dispatch(resetUser());
      await signOut();
      router.push('/');
      showToast('You have been successfully logged out!', 'success');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const onClick = () => {
    setIsDrawerOpen(false);
    dispatch(toggleLoginModal());
  };

  return (
    <>
      <div className="text-black text-base">
        {/* Sign in or register btn */}
        {!session?.loggedIn && (
          <div className="flex flex-col md:flex-row md:items-center gap-3">
            <p
              className={`${playfair_display} font-normal text-lg text-black leading-[32.4px]`}
            >
              {/* Discounts, Ideas & reviews */}
              Inspiration, insight and discounts
            </p>
            <PrimaryButton
              btnText="Join for free or log in"
              className="p-3 text-sm hover:bg-navy hover:text-cruise"
              textColor="text-black"
              onClick={onClick}
              isArrowShow={false}
            />
          </div>
        )}

        {/* user logged in */}
        {session?.loggedIn && (
          <div className="flex items-center gap-3">
            <p className={`${playfair_display} text-base md:text-lg`}>
              Welcome back {session?.user?.firstname}
            </p>
            <div
              className={`flex items-center bg-white p-2 gap-4 divide-x ${playfair_display}`}
            >
              {/* <a
                href="#"
                className="cursor-pointer text-black hover:underline "
              >
                My Profile
              </a> */}
              <span
                onClick={handleLogout}
                className="cursor-pointer text-sm text-black hover:underline"
              >
                Logout
              </span>
            </div>
          </div>
        )}
        {/* user logged in */}
      </div>

      {/* Success modal */}
      {!!Object.keys(showSuccessModal).length && (
        <SuccessfulModal
          showSuccessModal={showSuccessModal}
          setShowSuccessModal={setShowSuccessModal}
        />
      )}
    </>
  );
};

export default UserStatus;
