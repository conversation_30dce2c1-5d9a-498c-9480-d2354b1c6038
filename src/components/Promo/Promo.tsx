import { useAppDispatch, useAppSelector } from '@/libs/hooks';
import PrimaryButton from '../PrimaryButton';
import { PromoItem } from './PromoItem';
import { toggleLoginModal } from '@/libs/store/setting';
import { playfair_display } from '@/utils/fonts';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

const Promo = ({ title = '', data = [] }) => {
  const user = useAppSelector((state) => state.user);
  const dispatch = useAppDispatch();
  const setting = useAppSelector((state) => state.setting);
  const router = useRouter();
  const [openModal, setOpenModal] = useState(false);

  const redirectToPage = () => {
    router.push('/exclusive-discount');
  };

  const handleOnclick = () => {
    if (user.loggedIn) {
      redirectToPage();
    } else {
      setOpenModal(true);
      dispatch(toggleLoginModal());
    }
  };

  useEffect(() => {
    if (user.loggedIn && openModal) {
      redirectToPage();
    }
  }, [user.loggedIn]);

  return (
    <div className="flex flex-col items-center justify-center p-5">
      {title && (
        <h2
          className={`text-2xl md:text-[34px] text-black text-center font-normal leading-normal ${playfair_display} mb-10`}
        >
          {title}
        </h2>
      )}

      <div className="flex flex-col lg:flex-row gap-10 md:gap-[80px] w-full">
        {data.map((item, idx) => (
          <PromoItem key={idx} item={item} idx={idx} />
        ))}
      </div>

      <div className="text-center mt-10">
        <PrimaryButton
          textColor="text-black"
          btnText={
            !user.loggedIn ? 'Join for free today' : 'Our Exclusive Discounts'
          }
          className="text-lg hover:bg-navy hover:text-cruise group/edit"
          onClick={() => handleOnclick()}
          iconColor='text-black group-hover/edit:text-cruise'
        />
      </div>
    </div>
  );
};

export default Promo;
