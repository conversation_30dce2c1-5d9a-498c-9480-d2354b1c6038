import FullScreenCard from '../Card/FullScreenCard';

// Reusable component to render FullScreenCard based on the data
const RenderCards = ({ items, page, text }) => {
  return (
    <>
      {items.map((item, index) => (
        <FullScreenCard
          key={item.id}
          data={item}
          index={index}
          page={page}
          text={text}
        />
      ))}
    </>
  );
};

const ExploreMoreBySeaBlock = ({ data, page = '' }) => {

  // Destructure the data object
  const { cruise_lines, destination_guides, interests } = data[0] || {};

  // Function to decide what to render based on available data
  const renderContent = () => {
    if (destination_guides?.data?.length >= 2) {
      return (
        <RenderCards
          items={destination_guides.data.slice(0, 2)}
          page="destination"
          text="CRUISE DESTINATIONS"
        />
      );
    } else if (cruise_lines?.data?.length >= 2) {
      return (
        <RenderCards
          items={cruise_lines.data.slice(0, 2)}
          page="cruise-line"
          text="CRUISE LINES"
        />
      );
    } else if (interests?.data?.length >= 2) {
      return (
        <RenderCards
          items={interests.data.slice(0, 2)}
          page="interest"
          text="CRUISE INTEREST"
        />
      );
    } else if (
      destination_guides?.data?.length === 1 &&
      interests?.data?.length === 1
    ) {
      return (
        <>
          <RenderCards
            items={destination_guides.data}
            page="destination"
            text="CRUISE DESTINATIONS"
          />
          <RenderCards
            items={interests.data}
            page="interest"
            text="CRUISE INTERESTS"
          />
        </>
      );
    } else if (
      destination_guides?.data?.length === 1 &&
      cruise_lines?.data?.length === 1
    ) {
      return (
        <>
          <RenderCards
            items={destination_guides?.data}
            page="destination"
            text="CRUISE DESTINATIONS"
          />
          <RenderCards
            items={cruise_lines?.data}
            page="cruise-line"
            text="CRUISE LINES"
          />
        </>
      );
    } else if (
      interests?.data?.length === 1 &&
      cruise_lines?.data?.length === 1
    ) {
      return (
        <>
          <RenderCards
            items={interests.data}
            page="interest"
            text="CRUISE INTEREST"
          />
          <RenderCards
            items={cruise_lines.data}
            page="cruise-line"
            text="CRUISE LINES"
          />
        </>
      );
    } else if (destination_guides?.data?.length === 1) {
      return (
        <RenderCards
          items={destination_guides.data}
          page="destination"
          text="CRUISE DESTINATIONS"
        />
      );
    } else if (cruise_lines?.data?.length === 1) {
      return (
        <RenderCards
          items={cruise_lines.data}
          page="cruise-line"
          text="CRUISE LINES"
        />
      );
    } else if (interests?.data?.length === 1) {
      return (
        <RenderCards
          items={interests.data}
          page="interest"
          text="CRUISE INTEREST"
        />
      );
    }

    return null;
  };

  return renderContent() || <></>;
};

export default ExploreMoreBySeaBlock;
