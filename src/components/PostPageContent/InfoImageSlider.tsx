import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/navigation';
import { Autoplay, Navigation } from 'swiper';
import Image from 'next/image';
import 'swiper/css/bundle';
import PrevBtn from '@/assets/svg/swiper-btn/prev.svg';
import PrevActiveBtn from '@/assets/svg/swiper-btn/prev-active.svg';
import NextBtn from '@/assets/svg/swiper-btn/next.svg';
import NextActiveBtn from '@/assets/svg/swiper-btn/next-active.svg';
import { useState } from 'react';

const InfoImageSlider = ({ data }) => {
  const [isAtBeginning, setIsAtBeginning] = useState(true);
  const [isAtEnd, setIsAtEnd] = useState(false);

  return (
    <div className="w-full h-full relative">
      <Swiper
        navigation={{
          prevEl: '.rich-content-swiper-button-prev',
          nextEl: '.rich-content-swiper-button-next',
        }}
        modules={[Navigation, Autoplay]}
        onReachBeginning={() => setIsAtBeginning(true)}
        onReachEnd={() => setIsAtEnd(true)}
        onFromEdge={() => {
          setIsAtBeginning(false);
          setIsAtEnd(false);
        }}
      >
        {data?.map((sliderItem) => (
          <SwiperSlide key={sliderItem?.id}>
            <Image
              src={sliderItem?.attributes?.url}
              alt={sliderItem?.attributes?.name || ''}
              fill
              style={{ objectFit: 'cover', objectPosition: 'center' }}
              priority={true}
            />
          </SwiperSlide>
        ))}
      </Swiper>
      <div className="rich-content-swiper-button-prev bg-paper h-12 w-12 hidden  justify-center items-center md:flex">
        {isAtBeginning ? <PrevBtn /> : <PrevActiveBtn />}
      </div>
      <div className="rich-content-swiper-button-next bg-paper h-12 w-12 hidden  justify-center items-center md:flex">
        {isAtEnd ? <NextBtn /> : <NextActiveBtn />}
      </div>
    </div>
  );
};

export default InfoImageSlider;
