import { useRef, useState } from 'react';
import RightSection from './InfoRightContent';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import { Navigation, Pagination } from 'swiper';
import Image from 'next/image';
import 'swiper/css/navigation';
import 'swiper/css/bundle';
import PrevBtn from '@/assets/svg/swiper-btn/prev.svg';
import PrevActiveBtn from '@/assets/svg/swiper-btn/prev-active.svg';
import NextBtn from '@/assets/svg/swiper-btn/next.svg';
import NextActiveBtn from '@/assets/svg/swiper-btn/next-active.svg';

type DataType = {
  id: number;
  tab_title: string;
  image: {
    data: Array<{
      attributes: {
        url: string;
        name?: string;
      };
    }>;
  };
};

const InfoBoxSlider = ({ data }: { data: DataType[] }) => {
  const swiperRef = useRef<any>(null); // Ref to access the Swiper instance
  const [isAtBeginning, setIsAtBeginning] = useState(true);
  const [isAtEnd, setIsAtEnd] = useState(false);
  const [activeIndex, setActiveIndex] = useState<number>(0);

  const handleTabClick = (index: number) => {
    setActiveIndex(index);
    // Check if the swiper instance is available before calling slideTo
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideTo(index);
    }
  };

  const handleSlideChange = (swiper) => {
    setIsAtBeginning(swiper.isBeginning);
    setIsAtEnd(swiper.isEnd);
    setActiveIndex(swiper?.activeIndex);
  };

  return (
    <>
      {/* Tabs */}
      <div className="text-sm font-medium text-center text-gray-500 border-gray-200 dark:text-gray-400 dark:border-gray-700 px-5 md:px-0 hidden md:block">
        <ul className="flex flex-no-wrap gap-8">
          {data?.map((item, index) => (
            <a
              key={`tab-${item.id}`}
              onClick={() => handleTabClick(index)}
              className={`inline-block pt-8 pb-4 ${
                activeIndex === index ? 'border-b-2 text-orange' : ''
              } cursor-pointer rounded-t-lg hover:text-orange uppercase`}
            >
              {item.tab_title}
            </a>
          ))}
        </ul>
      </div>

      {/* Swiper Slider */}
      <div className="content-image-container relative">
        <Swiper
          ref={swiperRef}
          spaceBetween={10}
          slidesPerView={2}
          modules={[Navigation, Pagination]}
          pagination={{
            clickable: true,
            el: '.swiper-pagination',
            type: 'bullets',
          }}
          navigation={{
            prevEl: '.rich-content-swiper-button-prev',
            nextEl: '.rich-content-swiper-button-next',
          }}
          onSlideChange={handleSlideChange}
          onSwiper={(swiper) => {
            setIsAtBeginning(swiper.isBeginning);
            setIsAtEnd(swiper.isEnd);
          }}
          breakpoints={{
            320: { slidesPerView: 1.1, spaceBetween: 10 },
            640: { slidesPerView: 1, spaceBetween: 10 },
            1024: { slidesPerView: 1, spaceBetween: 10 },
          }}
        >
          {data.map((item, index) => (
            <SwiperSlide key={index}>
              <div className="flex flex-col md:flex-row min-h-[236px]">
                {/* Left Image Section */}
                <div className="h-[236px] md:h-auto sm:min-h-[320px] md:min-h-[550px] lg:min-h-[500px] w-full md:w-3/5 lg:w-4/6 relative flex-grow">
                  {item.image?.data && (
                    <Image
                      src={
                        item.image.data[0]?.attributes?.url || '/fallback.jpg'
                      }
                      alt={item.image.data[0]?.attributes?.name || 'Image'}
                      fill
                      style={{ objectFit: 'cover', objectPosition: 'center' }}
                      priority={true}
                    />
                  )}
                </div>

                {/* Right Content Section */}
                <div className="bg-[rgba(220,218,214,1)] px-4 py-4 md:py-10 w-full md:w-2/5 lg:w-2/6 flex-grow flex items-stretch">
                  <RightSection data={item} />
                </div>
              </div>
            </SwiperSlide>
          ))}
          <div className="swiper-pagination static flex gap-1.5 md:hidden"></div>
        </Swiper>

        {/* Navigation Buttons */}
        <div
          className="rich-content-swiper-button-prev bg-paper h-12 w-12 hidden  justify-center items-center md:flex"
          id="previousBtn"
        >
          {isAtBeginning ? <PrevBtn /> : <PrevActiveBtn />}
        </div>
        <div
          className="rich-content-swiper-button-next bg-paper h-12 w-12 hidden  justify-center items-center md:flex"
          id="nextBtn"
        >
          {isAtEnd ? <NextBtn /> : <NextActiveBtn />}
        </div>
      </div>
    </>
  );
};

export default InfoBoxSlider;
