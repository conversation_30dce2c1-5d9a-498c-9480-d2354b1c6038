const JoinUsImageBlock = ({ data }) => {
  if (!data || data.length === 0) return null;

  return (
    <div className="py-6">
      <div className="w-full">
        {data.map((item) => (
          <div
            key={item.id}
            className="bg-white shadow-lg rounded-lg overflow-hidden mb-6"
          >
            {item.image?.data?.attributes?.url ? (
              <img
                src={item.image.data.attributes.url}
                alt={`Cruise Image ${item.id}`}
                className="w-full h-64 md:h-80 object-cover rounded-lg"
              />
            ) : null}
          </div>
        ))}
      </div>
    </div>
  );
};

export default JoinUsImageBlock;
