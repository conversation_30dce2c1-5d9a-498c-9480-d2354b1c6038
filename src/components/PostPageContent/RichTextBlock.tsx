import { playfair_display } from '@/utils/fonts';

const RichTextBlock = ({ data }) => {
  const block = data[0];
  const { name = '', Text = '', content = '' } = block;

  // blocks.rich-text
  const textContent = Text || content || name;

  return (
    <div
      className={`${playfair_display} font-normal w-full md:max-w-[1016px] mx-auto leading-normal`}
    >
      {textContent && <div dangerouslySetInnerHTML={{ __html: textContent }} />}
    </div>
  );
};

export default RichTextBlock;
