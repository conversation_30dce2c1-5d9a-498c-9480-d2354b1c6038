const ImageBlock = ({ data }) => {
  if (!data || data.length === 0) return null;

  return (
    <div className="flex justify-between items-center py-12">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 w-full">
        {data.map((item) => (
          <div
            key={item.id}
            className="bg-white shadow-lg rounded-lg overflow-hidden"
          >
            {item.image?.data?.attributes?.url ? (
              <img
                src={item.image.data.attributes.url}
                alt={`Cruise Image ${item.id}`}
                className="w-full object-cover rounded-lg"
              />
            ) : null}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ImageBlock;
