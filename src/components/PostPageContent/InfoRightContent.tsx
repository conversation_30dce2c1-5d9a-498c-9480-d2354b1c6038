import { playfair_display } from '@/utils/fonts';
import ArrowButton from '../Shared/ArrowButton';

const InfoRightContent = ({ data }) => {
  const { title = '', url_text = '', url, description = '' } = data;
  return (
    <div className="h-full flex flex-col justify-end">
      {/* <div
        className={`text-lg md:text-2xl text-black ${playfair_display} leading-normal font-normal`}
      >
        {title}
      </div> */}
      <div
        className={`${playfair_display} leading-normal font-normal info-box-editor`}
        dangerouslySetInnerHTML={{ __html: description }}
      ></div>

      {url && (
        <div
          className={`border flex gap-3 mt-4 items-end p-2 border-brand hover:bg-brand cursor-pointer group`}
        >
          <div
            className={`text-sm text-left text-black leading-[21px] pointer font-normal ${playfair_display}`}
          >
            <a href={`${url}`} target="_blank">
              {url_text}
            </a>
          </div>
          <div className="text-brand group-hover:text-black">
            <ArrowButton />
          </div>
        </div>
      )}
    </div>
  );
};

export default InfoRightContent;
