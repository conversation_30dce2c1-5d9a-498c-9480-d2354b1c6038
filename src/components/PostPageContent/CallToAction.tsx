import Stamp from '@/assets/svg/cc-stamp.svg';
import ContentBanner from '../CTABanner/ContentBanner';
import DiscountList from '../CTABanner/DiscountList';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Navigation } from 'swiper';
import { Discount } from '../SavingBlock/Discount';
import PrimaryButton from '../PrimaryButton';
import clsx from 'clsx';
import { playfair_display } from '@/utils/fonts';
import { useAppDispatch, useAppSelector } from '@/libs/hooks';
import { useRouter } from 'next/router';
import { toggleLoginModal } from '@/libs/store/setting';
import { useEffect, useState } from 'react';

const CallToAction = ({ data }) => {
  if (!data?.length) return null;
  const { checklist, claim_texts, cruise_lines, heading, description, sort } =
    data[0];
  const user = useAppSelector((state) => state.user);
  const dispatch = useAppDispatch();
  const setting = useAppSelector((state) => state.setting);
  const router = useRouter();
  const [openModal, setOpenModal] = useState(false);

  const redirectToPage = () => {
    router.push('/exclusive-discount');
  };

  const handleOnclick = () => {
    if (user.loggedIn) {
      redirectToPage();
    } else {
      setOpenModal(true);
      dispatch(toggleLoginModal());
    }
  };

  useEffect(() => {
    if (user.loggedIn && openModal) {
      redirectToPage();
    }
  }, [user.loggedIn]);

  return (
    <>
      <div className="min-w-screen bg-orange pl-4 py-10 md:pl-10 md:pr-10 md:py-[80px]">
        <div className="flex flex-col lg:flex-row gap-4 md:gap-10 mx-auto container">
          <div className="flex-none w-full md:w-[100px] mb-2 md:mb-0">
            <Stamp width="100" height="100" viewBox="0 0 144 146" />
          </div>
          {sort === 1 ? (
            <>
              <div className="flex-1 mb-2 md:mb-0">
                <div>
                  <div
                    className={`text-2xl md:text-[34px] text-black font-normal leading-normal ${playfair_display}`}
                  >
                    {heading}
                  </div>
                  <p
                    className={`text-base md:text-lg font-normal text-black ${playfair_display} mt-2`}
                  >
                    {description}
                  </p>
                </div>
              </div>
              <div className="flex w-full md:items-end md:w-[300px]">
                <div className="text-center mt-5 md:mt-10">
                  <PrimaryButton
                    btnText={
                      !user.loggedIn
                        ? 'Join for free today'
                        : 'Our Exclusive Discounts'
                    }
                    textColor="text-black"
                    type="primary"
                    className="text-lg hover:bg-navy hover:text-cruise group/edit"
                    onClick={() => handleOnclick()}
                    iconColor="text-black group-hover/edit:text-cruise"
                  />
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="mb-2 md:mb-0">
                  <ContentBanner item={data[0]} />
                </div>
                <div>
                  {sort === 3 && (
                    <div className="flex flex-col mt-4 lg:mt-10">
                      {checklist?.map((item) => (
                        <DiscountList
                          key={`discount-list-${item?.id}`}
                          item={item}
                        />
                      ))}
                    </div>
                  )}

                  {sort === 2 && (
                    <div className="w-full mt-4 md:mt-10">
                      <Swiper
                        spaceBetween={0}
                        slidesPerView={3}
                        modules={[Autoplay, Navigation]}
                        breakpoints={{
                          320: {
                            slidesPerView: 1.2,
                            spaceBetween: 10,
                          },
                          480: {
                            slidesPerView: 1.2,
                            spaceBetween: 10,
                          },
                          640: {
                            slidesPerView: 1.8,
                            spaceBetween: 5,
                          },
                          768: {
                            slidesPerView: 3,
                            spaceBetween: 5,
                          },
                          920: {
                            slidesPerView: 4,
                            spaceBetween: 5,
                          },
                          1024: {
                            slidesPerView: 2.1,
                            spaceBetween: 5,
                          },
                          1080: {
                            slidesPerView: 2.3,
                            spaceBetween: 5,
                          },
                          1130: {
                            slidesPerView: 2.5,
                            spaceBetween: 5,
                          },
                          1300: {
                            slidesPerView: 3,
                            spaceBetween: 5,
                          },
                        }}
                      >
                        {cruise_lines?.data?.map((item, index) => (
                          <SwiperSlide key={index}>
                            <Discount
                              item={item}
                              text={item?.attributes?.calloutbox?.sub_heading}
                              btnFontSize="text-sm"
                              width="163px"
                              height="163px"
                            />
                          </SwiperSlide>
                        ))}
                      </Swiper>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default CallToAction;
