import { playfair_display } from '@/utils/fonts';

const TextBlock = ({ data }) => {
  const block = data[0];
  const { name = '', Text = '', content = '' } = block;

  // blocks.rich-text
  const textContent = Text || content || name;

  return (
    <div
      className={`py-10 md:py-[70px] text-xl md:text-[34px] ${playfair_display} font-normal w-full md:max-w-[1016px] mx-auto leading-normal`}
    >
      {textContent && <div dangerouslySetInnerHTML={{ __html: textContent }} />}
    </div>
  );
};

export default TextBlock;
