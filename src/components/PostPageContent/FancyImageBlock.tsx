import { playfair_display } from '@/utils/fonts';
import clsx from 'clsx';
import Image from 'next/image';

const FancyImageBlock = ({ data }) => {
  const { title = '', text_alignment = '', image, description } = data[0];
  const alignmentClasses =
    text_alignment === 'left'
      ? 'right-4 md:left-0'
      : 'right-0 left-4 md:left-auto md:right-0';

  return (
    <div className="pt-[80px] pb-[80px]">
      {/* Image Section */}
      <div
        className={clsx('flex', {
          // 'border-b': showChild,
          'justify-start': text_alignment === 'right',
          'justify-end': text_alignment == 'left',
        })}
      >
        <div className="image-section w-full md:w-3/4 h-[508px] md:h-[700px] relative">
          {image?.data?.attributes?.url && (
            <div className="w-full">
              <Image
                src={image?.data?.attributes?.url}
                alt="Cello Project Image"
                fill
                sizes="(max-width: 768px) 100vw, 75vw"
                loading="lazy"
                className="object-cover md:object-contain"
              />
            </div>
          )}
        </div>
      </div>

      {/* Content Section */}
      <div className="relative mb-[120px] md:mb-0">
        <div
          className={clsx(
            `flex absolute z-20 -bottom-44 md:-bottom-20 ${alignmentClasses}`,
            {
              // '-bottom-44 md:-bottom-20': fancyData?.text_alignment === 'right',
              // '-bottom-44 md:-bottom-20': fancyData?.text_alignment == 'left',
            },
          )}
        >
          <div
            className="max-w-[554px] px-4 py-4 md:py-10"
            style={{ backgroundColor: 'rgba(245, 242, 238, 1)' }}
          >
            <div
              className={`text-xl md:text-[34px] text-black font-normal leading-normal ${playfair_display}`}
            >
              {title}
            </div>
            <div
              className={`text-base md:text-lg font-normal text-black ${playfair_display} mt-4 md:mt-10 leading-[28.8px] md:leading-[32.4px]`}
            >
              {description}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FancyImageBlock;
