import Quote from '@/assets/svg/quote.svg';
import { playfair_display } from '@/utils/fonts';

const TestimonialBlock = ({ data }) => {
  const { author, description, image } = data[0];
  const imgUrl =
    'https://project-cruise.s3.eu-west-2.amazonaws.com/thumbnail_CELLO_exterior_4c3128025a.jpg';
  return (
    <div
      className="bg-cover bg-center bg-no-repeat w-full h-[600px] flex md:justify-center items-end md:items-center px-4 py-10"
      style={{
        background: `linear-gradient(0deg, rgba(35, 49, 65, 0.50) 0%, rgba(35, 49, 65, 0.50) 100%), url('${image?.data?.attributes?.url}')`,
      }}
    >
      <div className="mx-auto md:max-w-[1016px]">
        <div className="flex md:justify-center">
          <Quote />
        </div>

        <div
          className={`md:mt-6 ${playfair_display} font-normal w-full mx-auto text-white text-2xl md:text-[34px] leading-normal md:text-center`}
        >
          <span>"</span>
          {description}
          <span>"</span>
        </div>
        <div
          className={`${playfair_display} font-normal w-full flex md:justify-center text-brand mt-4 md:mt-10  text-base md:text-[34px]`}
        >
          {author}
        </div>
      </div>
    </div>
  );
};

export default TestimonialBlock;
