import { playfair_display } from '@/utils/fonts';
import PrimaryButton from '../PrimaryButton';
import { useRouter } from 'next/router';
import DefaultMetaTitle from '../DefaultMetaTitle';
import { getPuzzleMeta } from '@/utils/puzzleHelper';

interface MemberPuzzlesBlockProps {
  button?: boolean;
  puzzles?: any[];
  loading?: boolean;
}

const MemberPuzzlesBlock = ({ button = true, puzzles = [], loading = false }: MemberPuzzlesBlockProps) => {
  const router = useRouter();

  const handleOnclick = () => {
    router.push('/puzzle');
  };

  return (
    <>
      <DefaultMetaTitle title={'Puzzle'} />
      <div className="w-full py-10 md:py-[80px] bg-[#233141]">
        <div className="container mx-auto px-4 md:px-10 text-center">
          <h2
            className={`text-2xl md:text-[34px] text-white font-normal ${playfair_display} mb-6`}
          >
            Member Puzzles
          </h2>

          <div className="flex justify-center mb-6">
            <img
              src="/images/icons/puzzle.png"
              alt="Puzzle"
              className="w-20 h-20"
            />
          </div>

          <p className={`text-base md:text-lg text-white max-w-2xl mx-auto `}>
            Your first go is on us — to unlock the full set, sign up for free
            today.
          </p>

          {button && (
            <PrimaryButton
              textColor="text-black mt-6"
              btnText="PLAY"
              className="text-lg hover:bg-navy hover:text-cruise group/edit inline-flex items-center"
              onClick={() => handleOnclick()}
            />
          )}

          {/* Display puzzle data if available */}
          {!button && puzzles.length > 0 && (
            <div className="mt-8">
              <div className="max-w-4xl mx-auto">
                {loading ? (
                  <div className="text-white text-center py-4">Loading puzzles...</div>
                ) : (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {puzzles.slice(0, 8).map((puzzle: any, idx: number) => {
                      const { puzzleName, iconPath } = getPuzzleMeta(puzzle);
                      return (
                        <div
                          key={puzzle.pml_id || idx}
                          className="bg-white bg-opacity-10 rounded-lg p-4 text-center hover:bg-opacity-20 transition-all duration-300"
                        >
                          <img
                            src={iconPath}
                            alt={puzzle.name}
                            className="w-12 h-12 object-contain rounded mx-auto mb-2"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/images/default.jpg';
                            }}
                          />
                          <span className="text-white text-sm font-medium block">
                            {puzzleName}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                )}
                {puzzles.length > 8 && (
                  <div className="text-center mt-4">
                    <span className="text-white text-sm">
                      And {puzzles.length - 8} more puzzles available...
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default MemberPuzzlesBlock;
