import { playfair_display } from '@/utils/fonts';
import PrimaryButton from '../PrimaryButton';
import { useRouter } from 'next/router';
import DefaultMetaTitle from '../DefaultMetaTitle';

interface MemberPuzzlesBlockProps {
  button?: boolean;
  puzzles?: any;
  loading?: boolean;
}

const MemberPuzzlesBlock = ({ button = true, puzzles = null, loading = false }: MemberPuzzlesBlockProps) => {
  const router = useRouter();

  const handleOnclick = () => {
    router.push('/puzzle');
  };

  // Extract data from API response
  const heading = puzzles?.heading || 'Member Puzzles';
  const description = puzzles?.description || 'Your first go is on us — to unlock the full set, sign up for free today.';
  const buttonText = puzzles?.button_text || 'PLAY';

  return (
    <>
      <DefaultMetaTitle title={'Puzzle'} />
      <div className="w-full py-10 md:py-[80px] bg-[#233141]">
        <div className="container mx-auto px-4 md:px-10 text-center">
          <h2
            className={`text-2xl md:text-[34px] text-white font-normal ${playfair_display} mb-6`}
          >
            {heading}
          </h2>

          <div className="flex justify-center mb-6">
            <img
              src="/images/icons/puzzle.png"
              alt="Puzzle"
              className="w-20 h-20"
            />
          </div>

          <p className={`text-base md:text-lg text-white max-w-2xl mx-auto `}>
            {description}
          </p>

          {button && (
            <PrimaryButton
              textColor="text-black mt-6"
              btnText={buttonText}
              className="text-lg hover:bg-navy hover:text-cruise group/edit inline-flex items-center"
              onClick={() => handleOnclick()}
            />
          )}

          {/* Display loading state or additional content */}
          {!button && loading && (
            <div className="mt-8">
              <div className="text-white text-center py-4">Loading puzzle data...</div>
            </div>
          )}

          {/* Display puzzle data if available and not showing button */}
          {!button && puzzles && !loading && (
            <div className="mt-8">
              <div className="max-w-4xl mx-auto">
                <div className="bg-white bg-opacity-10 rounded-lg p-6 text-center">
                  <h3 className="text-white text-xl font-semibold mb-4">
                    {heading}
                  </h3>
                  <p className="text-white text-base mb-4">
                    {description}
                  </p>
                  <div className="text-white text-sm opacity-75">
                    <p>Created: {new Date(puzzles.createdAt).toLocaleDateString()}</p>
                    <p>Updated: {new Date(puzzles.updatedAt).toLocaleDateString()}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default MemberPuzzlesBlock;
