import { playfair_display } from '@/utils/fonts';
import PrimaryButton from '../PrimaryButton';
import { useRouter } from 'next/router';
import DefaultMetaTitle from '../DefaultMetaTitle';

const MemberPuzzlesBlock = ({ button = true, puzzle = null }) => {
  const router = useRouter();

  const handleOnclick = () => {
    router.push('/puzzle');
  };

  // Extract data from API response
  const heading = puzzle?.heading || 'Member Puzzles';
  const description =
    puzzle?.description ||
    'Your first go is on us — to unlock the full set, sign up for free today.';
  const buttonText = puzzle?.button_text || 'PLAY';

  return (
    <>
      <DefaultMetaTitle title={'Puzzle'} />
      <div className="w-full py-10 md:py-[80px] bg-[#233141]">
        <div className="container mx-auto px-4 md:px-10 text-center">
          <h2
            className={`text-2xl md:text-[34px] text-white font-normal ${playfair_display} mb-6`}
          >
            {heading}
          </h2>

          <div className="flex justify-center mb-6">
            <img
              src="/images/icons/puzzle.png"
              alt="Puzzle"
              className="w-20 h-20"
            />
          </div>

          <p className={`text-base md:text-lg text-white max-w-2xl mx-auto `}>
            {description}
          </p>

          {button && (
            <PrimaryButton
              textColor="text-black mt-6"
              btnText={buttonText}
              className="text-lg hover:bg-navy hover:text-cruise group/edit inline-flex items-center"
              onClick={() => handleOnclick()}
            />
          )}
        </div>
      </div>
    </>
  );
};

export default MemberPuzzlesBlock;
