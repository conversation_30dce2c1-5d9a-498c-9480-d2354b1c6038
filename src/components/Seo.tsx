import Head from 'next/head';
import React from 'react';

const Seo = ({ data }) => {
  const {
    title = null,
    description = null,
    url = null,
    shareImage = null,
    keywords = null,
    preventIndexing = null,
    metaTitle = null,
    metaDescription = null,
    metaImage = null,
    metaSocial = null,
    canonicalURL = null,
    metaRobots = null,
    structuredData = null,
    metaViewport = null,
  } = data;

  return (
    <Head>
      <title>
        {(metaTitle || title)
          ? `Cruise Collective | ${metaTitle || title}`
          : 'Cruise Collective'}
      </title>

      <meta
        name="description"
        content={metaDescription || description}
        key="description"
      />
      <meta name="keywords" content={keywords || ''} />
      {/* Loop through each social component in metaSocial */}
      {metaSocial?.map((socialComponent, index) => (
        <React.Fragment key={index}>
          <meta
            name={`og:locale:${index}`}
            content={socialComponent.socialNetwork}
          />
          <meta name={`og:title:${index}`} content={socialComponent.title} />
          <meta
            name={`og:description:${index}`}
            content={socialComponent.description}
          />
          <meta name={`og:image:${index}`} content={socialComponent.image} />
        </React.Fragment>
      ))}

      <meta property="og:url" content={canonicalURL || url} key="og:url" />
      <meta
        property="og:title"
        content={
          metaTitle || title
            ? `Cruise Collective | ${metaTitle || title}`
            : 'Cruise Collective'
        }
        key="og:title"
      />

      <meta
        property="og:description"
        content={metaDescription || description}
        key="og:description"
      />
      <meta
        property="og:image"
        content={metaImage || shareImage}
        key="og:image"
      />
      <link rel="canonical" href={canonicalURL || url} />

      {preventIndexing && (
        <>
          <meta name="robots" content={metaRobots || 'noindex'} />
          <meta name="googlebot" content={metaRobots || 'noindex'} />
        </>
      )}

      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}

      {metaViewport && <meta name="viewport" content={metaViewport} />}
    </Head>
  );
};

export default Seo;
