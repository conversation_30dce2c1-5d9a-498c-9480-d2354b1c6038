import { FullScreenImageSlider } from '@/utils';
import { playfair_display } from '@/utils/fonts';

const ArticleSlider = ({ data, children, bannerText = '' }) => {
  const showPartnership = () => {
    const partnership = data?.partnership;
    if (!partnership?.is_partnership) return null;

    const text = partnership?.organisation || 'Paid Partnership';

    return (
      <div className="flex items-center justify-center mt-4">
        <div className="bg-brand text-white font-semibold px-3 py-1">
          {text}
        </div>
      </div>
    );
  };
  return (
    <div className="md:h-screen md:px-10">
      <div className="w-full h-[400px] md:h-[80vh] ">
        <FullScreenImageSlider
          source="inspiration"
          sliderItems={data?.sliders}
          video={data?.video}
        />
        {children && children}
      </div>
      <div className="relative md:h-[20vh]">
        <div className="flex w-full md:w-3/4 mx-auto bg-black-transparent-rgba relative md:absolute bottom-0 left-1/2 transform -translate-x-1/2 text-center z-10 px-4 md:py-10 md:px-10">
          <div className="flex w-full justify-center lg:w-[1016px] mx-auto py-10 md:py-[80px]">
            <div className="w-full relative">
              <div className="text-sm md:text-2xl text-brand uppercase text-left md:text-center leading-[26.4px] font-medium mb-4 md:mb-10">
                {bannerText}
              </div>
              <div
                className={`text-5xl md:text-6xl text-white font-normal mt-3 text-left md:text-center ${playfair_display} mb-4 md:mb-10`}
              >
                {data?.heading}
              </div>
              <div
                dangerouslySetInnerHTML={{ __html: data?.excerpt }}
                className={`text-base md:text-2xl text-white font-normal mt-3 text-left md:text-center article-slider ${playfair_display}`}
              ></div>
              {showPartnership()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArticleSlider;
