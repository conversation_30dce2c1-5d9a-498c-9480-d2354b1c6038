import { playfair_display } from '@/utils/fonts';
import React from 'react';
import PrimaryButton from '../PrimaryButton';
import { useRouter } from 'next/router';
import ArrowButton from '../Shared/ArrowButton';
import { ParallaxBanner } from 'react-scroll-parallax';

export const CategoryPromo = ({ data }) => {
  const isLeftAligned = data?.alignment === 'left';
  const button = data?.button;
  const image = data?.image?.data?.attributes?.url;
  const checklist = data?.checklist || [];
  const router = useRouter();

  const handleClick = () => {
    router.push(`/${button.url}`);
  };

  const Content = () => (
    <div>
      <h4 className="mb-4 text-brand text-sm font-medium leading-[110%] uppercase">
        {data?.subtitle}
      </h4>
      <h1
        className={`text-[48px] lg:text-[60px] font-normal text-white leading-[52.8px] lg:leading-[66px] ${playfair_display}`}
      >
        {data?.title}
      </h1>
      <p
        className={`${playfair_display} font-normal text-lg text-white leading-[32.4px]`}
      >
        {data?.description}
      </p>

      {button && (
        <button
          className="uppercase bg-transparent border-white border hover:bg-brand hover:text-black group px-4 py-3 flex items-center hover:border-brand mt-4"
          onClick={handleClick}
        >
          {button.name}
          <span className="ml-2 transition-colors duration-200 text-brand group-hover:text-black">
            <ArrowButton />
          </span>
        </button>
      )}

      <div className="lg:grid grid-cols-1 hidden lg:grid-cols-2 gap-4 mt-10">
        {checklist.map((link, index) => (
          <a key={index} href={link.url} className="flex">
            <span className="text-sm font-medium leading-[15.4px] uppercase text-white">
              {link.name}
            </span>
            <span className="ml-2 transition-colors duration-200 text-brand">
              <ArrowButton />
            </span>
          </a>
        ))}
      </div>
    </div>
  );

  return (
    <div className="relative text-white mt-10 h-[500px] lg:h-[700px] xl:h-auto">
    <ParallaxBanner
      className="h-full"
      style={{ aspectRatio: '2 / 1' }}
      layers={[
        {
          image,
          speed: -5,
        },
        {
          children: (<>
            <div
            className="absolute inset-0"
            style={{
              background: isLeftAligned
                ? `linear-gradient(253deg, rgba(11, 18, 55, 0) 0%, #0D1029 100%)`
                : `linear-gradient(107deg, rgba(11, 18, 55, 0.00) 0%, #0D1029 100%)`,
            }}
          />
            <div className="relative grid grid-cols-1 lg:grid-cols-2 gap-1 xl:gap-8 items-end lg:items-center h-full container mx-auto px-4 lg:px-10 py-10">
              {isLeftAligned ? <Content /> : <div className='hidden lg:block'></div>}
              {isLeftAligned ? <div className='hidden lg:block'></div> : <Content />}
            </div>
          </>),
        },
      ]}
    />
  </div>
  );
};

export default CategoryPromo;
