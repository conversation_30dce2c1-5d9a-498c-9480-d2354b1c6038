import { useRouter } from 'next/router';
import clsx from 'clsx';
import ArrowButton from './Shared/ArrowButton';

const PrimaryButton = ({
  href = '',
  btnText,
  textColor = 'text-white',
  className = '',
  onClick = () => {},
  disabled = false,
  type = 'primary',
  isArrowShow = true,
  iconColor = 'text-black',
}) => {
  const router = useRouter();

  const handleClick = (e) => {
    // @ts-ignore
    onClick(e); // Call the passed onClick function
    if (!e.defaultPrevented && href) {
      // Check if default action was prevented and if href exists
      router.push(href);
    }
  };

  return (
    <button
      disabled={disabled}
      className={clsx(
        `uppercase flex flex-nowrap items-center justify-center px-4 py-3 font-medium group-hover/edit ${textColor} ${className}`,
        {
          'bg-cruise': type === 'primary',
        },
        {
          'bg-transparent border-white border': type === 'outline',
        },
      )}
      onClick={handleClick}
    >
      {btnText}
      {isArrowShow && (
        <span className={clsx('ml-2 transition-colors duration-200', iconColor)}>
          <ArrowButton />
        </span>
      )}
    </button>
  );
};

export default PrimaryButton;
