import { useQuery } from 'react-query';
import { getHomePageData } from '@/queries';
import ArticleCard from '../Card/ArticleCard';
import Promo from '../Promo';
import SavingBlock from '../SavingBlock';
import CategoryPromo from '../CategoryPromo';
import CTABannerInfo from '../CTABanner/CTABannerInfo';
import { useEffect } from 'react';
import { useAppDispatch } from '@/libs/hooks';
import { setHomeData } from '@/libs/store/api';
import { playfair_display } from '@/utils/fonts';
import ParallaxBox from '../Shared/ParallaxBox';
import BlackFridayBanner from '../BlackFridayBanner';

const HomeLandingPage = () => {
  const { data } = useQuery('homepage', () => getHomePageData(), {
    refetchOnWindowFocus: false,
    enabled: true,
  });

  const dispatch = useAppDispatch();

  const articles = data?.articles || [];
  const sliders = data?.sliders || [];
  const categoryPromos = data?.category_promo || [];
  const promos = data?.promos || [];
  const countdown = data?.countdown;

  useEffect(() => {
    if (data) {
      dispatch(setHomeData(data));
    }
  }, [data]);

  return (
    <>
      {/* <ImageSlider sliderItems={sliders} /> */}

      <ParallaxBox
        image={sliders?.[0]?.image?.data?.attributes?.url}
        title={sliders?.[0]?.title}
        headline={sliders?.[0]?.description}
        video={sliders?.[0]?.video}
        isHomePage={true}
      />

      <div className="py-10 md:py-[4rem]">
        {countdown && (
          <div className="mb-10 px-4 md:px-10">
            <BlackFridayBanner data={countdown} />
          </div>
        )}

        <section className="mb-10 lg:mb-[80px] container mx-auto px-4 md:px-10">
          <Promo data={promos} title="How does it work?" />
        </section>

        <div className="mb-10 px-4 md:px-10">
          <SavingBlock />
        </div>

        <section className="py-10 my:py-[80px] container mx-auto px-4 md:px-10">
          <div
            className={`text-2xl md:text-[34px] text-black font-normal ${playfair_display} md:text-center mb-10`}
          >
            Latest articles from the collective
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {articles?.data?.slice(0, 5)?.map((article, index) => (
              <ArticleCard
                key={article.id}
                article={article}
                page="article"
                className={`${
                  index === 0 ? 'lg:col-span-2' : '' // Make the first item span 2 columns on md screens
                }`}
              />
            ))}
          </div>
        </section>

        <div className="md:px-10">
          {categoryPromos?.map((promo) => (
            <CategoryPromo data={promo} key={promo.id} />
          ))}
        </div>

        {data?.cta2 && (
          <section className="mt-4 md:mt-10 md:px-10">
            <CTABannerInfo data={data.cta2} />
          </section>
        )}
      </div>
    </>
  );
};

export default HomeLandingPage;
