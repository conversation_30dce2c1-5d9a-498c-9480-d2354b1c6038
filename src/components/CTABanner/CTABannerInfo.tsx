import Stamp from '@/assets/svg/cc-stamp.svg';
import ContentBanner from './ContentBanner';
import DiscountList from './DiscountList';

const CTABannerInfo = ({ data }: any) => {
  return (
    <div className="min-w-screen bg-orange pl-4 py-10 lg:pl-10 lg:pr-10 lg:py-[80px]">
      <div className="flex flex-col lg:flex-row gap-4 lg:gap-10 mx-auto container">
        <div className="flex-none w-full lg:w-[100px] mb-2 lg:mb-0">
          <Stamp width="100" height="100" viewBox="0 0 144 146" />
        </div>

        <div className="flex-1">
          <div className="grid grid-cols-1 lg:grid-cols-2">
            <div className="mb-2 md:mb-0">
              <ContentBanner item={data} />
            </div>
            <div>
              <div className="flex flex-col mt-4 lg:mt-10">
                {data?.checklist?.map((item) => (
                  <DiscountList
                    key={`discount-list-home-${item?.id}`}
                    item={item}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CTABannerInfo;
