import PrimaryButton from '@/components/PrimaryButton';
import 'swiper/css/navigation';
import 'swiper/css/bundle';

// Constants for reusable styles and configurations
const BUTTON_STYLES = 'text-sm hover:bg-navy hover:text-cruise min-w-[175px]';
const CARD_STYLES = 'bg-orange p-3 w-full border border-dashed border-paper';

const ArticleCTA = ({ data }) => {
  const firstItem = data?.[0];
  const {
    stamp_logo: logo,
    stamp_sub_text,
    stamp_text,
    description,
    button_text,
    button_link,
    button_target,
  } = firstItem;

  const handleButtonClick = () => {
    const target = button_target === '_blank' ? '_blank' : '_self';
    window.open(button_link, target);
  };

  return (
    <div className="flex flex-col lg:flex-row gap-6 lg:gap-2.5 w-full">
      {/* Logo and Saving Section */}
      <div className="flex flex-col gap-4 justify-center items-center">
        <div
          className="relative text-center items-center justify-center flex-col gap-2 bg-no-repeat bg-center bg-cover hidden md:flex"
          style={{
            backgroundImage: "url('/images/stamp-bg.png')",
            width: '195px',
            height: '100%',
          }}
        >
          {logo?.data?.attributes?.url && (
            <img
              src={logo?.data?.attributes?.url}
              alt="stamp"
              className="relative z-20 transform translate-y-[-20%] px-4"
            />
          )}
          <div className="text-orange text-[52px] font-bold relative z-20 transform">
            {stamp_text}
          </div>
          <div className="text-xs text-orange relative z-20 transform translate-y-[-40%]">
            {stamp_sub_text}
          </div>
        </div>
        <div className="flex gap-4 w-full md:hidden items-center">
          <div className="w-3/5 md:w-full">
            {logo?.data?.attributes?.url && (
              <img
                src={logo?.data?.attributes?.url}
                alt="stamp"
                className="relative z-20 transform translate-y-[-20%] px-4"
              />
            )}
          </div>
          <div className="w-2/5 md:w-full">
            <div className="text-brand text-[40px] text-center font-bold normal-case">
              {stamp_text}
            </div>
            <div className="text-xs text-orange-500 text-center uppercase">
              {stamp_sub_text}
            </div>
          </div>
        </div>
      </div>

      {/* Discount Details Section */}
      <div className={CARD_STYLES}>
        <div className="p-3 border-cruise border h-full">
          <div className="flex justify-center items-center flex-col md:flex-row gap-4 mt-2 h-full">
            <div>{description}</div>
            <PrimaryButton
              className={BUTTON_STYLES}
              textColor="text-black"
              btnText={button_text}
              isArrowShow={false}
              onClick={handleButtonClick}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArticleCTA;