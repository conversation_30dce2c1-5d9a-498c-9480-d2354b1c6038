import { playfair_display } from '@/utils/fonts';
import PrimaryButton from '../PrimaryButton';
import { useAppDispatch, useAppSelector } from '@/libs/hooks';
import { toggleLoginModal } from '@/libs/store/setting';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

const ContentBanner = ({ item, sort = 0 }: any) => {
  const user = useAppSelector((state) => state.user);
  const dispatch = useAppDispatch();
  // const setting = useAppSelector((state) => state.setting);
  const router = useRouter();
  const [openModal, setOpenModal] = useState(false);

  const redirectToPage = () => {
    router.push('/exclusive-discount');
  };

  const handleOnclick = () => {
    if (user.loggedIn) {
      redirectToPage();
    } else {
      setOpenModal(true);
      dispatch(toggleLoginModal());
    }
  };

  useEffect(() => {
    if (user.loggedIn && openModal) {
      redirectToPage();
    }
  }, [user.loggedIn]);

  if (!item) return null; // Temp fix for front-end

  return (
    <div>
      <div
        className={`text-2xl md:text-[34px] text-black font-normal leading-normal ${playfair_display}`}
      >
        {item.heading}
      </div>
      <p
        className={`text-base md:text-lg font-normal text-black ${playfair_display} mt-2`}
      >
        {item.description}
      </p>
      <div className="text-center mt-5 md:mt-10">
        <PrimaryButton
          btnText={
            !user.loggedIn ? 'Join for free today' : 'Our Exclusive Discounts'
          }
          textColor="text-black"
          type="primary"
          className="text-lg hover:bg-navy hover:text-cruise group/edit"
          onClick={() => handleOnclick()}
          iconColor='text-black group-hover/edit:text-cruise'
        />
      </div>
    </div>
  );
};

export default ContentBanner;
