import { RegistrationInput, RegistrationFormProps } from '@/types/registration';
import React, { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { postRegister } from '../queries/index';
import Link from 'next/link';
import { useRouter } from 'next/router';
import PasswordVisibleInvisible from './Shared/PasswordVisibleInvisible';
import 'react-datepicker/dist/react-datepicker.css';
import { signIn, useSession } from 'next-auth/react';
import { showToast } from '@/utils';
import { sendEmail } from '@/utils/email';
import { buildWelcomeEmail } from '@/utils/emailTemplate';
import PrimaryButton from './PrimaryButton';
import { loginUser } from '@/libs/store/user';
import { useAppDispatch } from '@/libs/hooks';

const RegistrationForm = ({
  campaign_id,
  adestra_id,
}: RegistrationFormProps) => {
  const router = useRouter();
  const { data: session } = useSession();
  const dispatch = useAppDispatch();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<RegistrationInput>();

  const [passwordVisible, setPassWordVisible] = useState(false);

  const [loading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const password = watch('password', '');

  const passwordRequirements = {
    length: password.length >= 8,
    uppercase: /[A-Z]/.test(password),
    number: /[0-9]/.test(password),
    special: /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password),
  };

  const isPasswordValid = Object.values(passwordRequirements).every(Boolean);

  const onSubmit: SubmitHandler<any> = async (data: any) => {
    try {
      setIsSubmitting(true);
      //User register
      const response: any = await postRegister({
        ...data,
        campaign_id,
        adestra_id,
      });

      if (response === false) {
        showToast(
          'This email already exist, please try another email',
          'warning',
        );
        return;
      }

      const { email: userEmail, password, firstname, lastname } = data;

      await sendEmail({
        to: userEmail,
        subject: 'Welcome Onboard from Cruise Collective',
        html: buildWelcomeEmail(firstname, lastname),
      });
      await signIn('credentials', {
        redirect: false,
        email: userEmail,
        password,
      });

      dispatch(loginUser(session?.user));
      showToast('Logged in successfully.', 'success');
      router.push('/exclusive-discount');
    } catch (error) {
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return <p className="mt-12">Loading...</p>;
  }

  return (
    <>
      <h2 className="text-base mb-4 mt-10 border-b opacity-20">
        Personal Information:
      </h2>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mt-10">
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2">
              First Name*
            </label>
            <input
              className="appearance-none border border-cruise rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              type="text"
              placeholder="First Name"
              {...register('firstname', { required: true })}
            />
            {errors.firstname && (
              <div className="text-red text-sm">Please enter a first name</div>
            )}
          </div>
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Surname (optional)
            </label>
            <input
              className="appearance-none border border-cruise rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              type="text"
              placeholder="Surname"
              {...register('lastname')}
            />
            {errors.lastname && (
              <div className="text-red text-sm">Please enter a last name</div>
            )}
          </div>

          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Email Address*
            </label>
            <input
              className="appearance-none border border-cruise rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              type="email"
              placeholder="Email Address"
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  value:
                    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                  message: 'Please enter a valid email',
                },
              })}
            />
            {errors.email && (
              <div className="text-red text-sm">
                Please enter a valid email address
              </div>
            )}
          </div>

          {/* Password and password confirm fields */}
          <div className="relative">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Password*
            </label>
            <input
              className="appearance-none border border-cruise rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              type={`${passwordVisible ? 'text' : 'password'}`}
              placeholder="Password"
              {...register('password', {
                required: 'Password is required',
                validate: {
                  strongPassword: (value) => {
                    if (!value) return 'Password is required';
                    if (value.length < 8)
                      return 'Password must be at least 8 characters long';
                    return true;
                  },
                },
              })}
            />

            <PasswordVisibleInvisible
              passwordVisible={passwordVisible}
              setPassWordVisible={setPassWordVisible}
            />

            {/* Password Strength Indicator */}
            {password && (
              <div className="mt-2 p-2 bg-gray-50 rounded border text-xs">
                {isPasswordValid ? (
                  <div className="p-1 bg-green-100 border border-green-300 rounded text-green-700 text-center">
                    ✓ Strong password
                  </div>
                ) : (
                  <div className="space-y-1">
                    <div
                      className={`flex items-center ${
                        passwordRequirements.length
                          ? 'text-green-600'
                          : 'text-gray-500'
                      }`}
                    >
                      <span
                        className={`w-3 h-3 mr-1 rounded-full flex items-center justify-center ${
                          passwordRequirements.length
                            ? 'bg-green-500'
                            : 'bg-gray-300'
                        }`}
                      >
                        {passwordRequirements.length ? '✓' : ''}
                      </span>
                      At least 8 characters
                    </div>
                    <div
                      className={`flex items-center ${
                        passwordRequirements.uppercase
                          ? 'text-green-600'
                          : 'text-gray-500'
                      }`}
                    >
                      <span
                        className={`w-3 h-3 mr-1 rounded-full flex items-center justify-center ${
                          passwordRequirements.uppercase
                            ? 'bg-green-500'
                            : 'bg-gray-300'
                        }`}
                      >
                        {passwordRequirements.uppercase ? '✓' : ''}
                      </span>
                      At least 1 uppercase letter
                    </div>
                    <div
                      className={`flex items-center ${
                        passwordRequirements.number
                          ? 'text-green-600'
                          : 'text-gray-500'
                      }`}
                    >
                      <span
                        className={`w-3 h-3 mr-1 rounded-full flex items-center justify-center ${
                          passwordRequirements.number
                            ? 'bg-green-500'
                            : 'bg-gray-300'
                        }`}
                      >
                        {passwordRequirements.number ? '✓' : ''}
                      </span>
                      At least 1 number
                    </div>
                    <div
                      className={`flex items-center ${
                        passwordRequirements.special
                          ? 'text-green-600'
                          : 'text-gray-500'
                      }`}
                    >
                      <span
                        className={`w-3 h-3 mr-1 rounded-full flex items-center justify-center ${
                          passwordRequirements.special
                            ? 'bg-green-500'
                            : 'bg-gray-300'
                        }`}
                      >
                        {passwordRequirements.special ? '✓' : ''}
                      </span>
                      At least 1 special character
                    </div>
                  </div>
                )}
              </div>
            )}

            {errors.password && (
              <div className="text-red text-sm mt-2">
                {errors.password.message}
              </div>
            )}
          </div>
        </div>

        {/* Agreement and Marketing Consent */}
        <div className="mb-4 mt-10">
          <label className="flex items-start mb-4 mt-8">
            <input
              type="checkbox"
              className="form-checkbox mt-1 mr-3"
              {...register('marketing')}
            />
            <span className="text-base text-gray-700 leading-relaxed">
              I would like Cruise Collective (operated by Our Media) to send me
              updates, exclusive offers, and promotions via email. You can
              unsubscribe at any time. For more details on how we handle your
              personal information, please refer to our{' '}
              <Link
                href="/privacy-policy"
                target="_blank"
                className="text-brand underline"
              >
                Privacy Policy
              </Link>
            </span>
          </label>
          <label className="flex items-start mb-4">
            <input
              type="checkbox"
              className="form-checkbox mt-1 mr-3"
              {...register('terms', { required: true })}
            />
            <span className="text-base text-gray-700 leading-relaxed">
              By signing up, you agree to our{' '}
              <Link
                href="/privacy-policy"
                target="_blank"
                className="text-brand underline"
              >
                Privacy Policy
              </Link>{' '}
              and{' '}
              <Link
                href="/terms-and-conditions"
                target="_blank"
                className="text-brand underline"
              >
                Terms & Conditions
              </Link>
            </span>
            {errors.terms && (
              <div className="text-red text-sm ml-2 mt-1">
                Please agree to the terms and conditions
              </div>
            )}
          </label>
        </div>
        {/* Register Button */}
        <div className="flex flex-col gap-4 items-start mb-2 mt-5">
          <PrimaryButton
            btnText={isSubmitting ? 'Loading...' : 'COMPLETE REGISTRATION'}
            type="primary"
            className={`text-lg hover:bg-navy hover:text-cruise group/edit ${
              isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            textColor="text-black"
            disabled={isSubmitting}
            isArrowShow={!isSubmitting}
          />
        </div>
      </form>
    </>
  );
};

export default RegistrationForm;
