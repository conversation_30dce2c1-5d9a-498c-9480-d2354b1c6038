import { RegistrationInput } from "@/types/registration";
import React, { useEffect, useState } from "react";
import { useForm, SubmitHandler } from "react-hook-form";
import { postRegister } from "../queries/index";
import countryList from "react-select-country-list";
import Link from "next/link";
import { useRouter } from "next/router";
import LoginModal from "./Modal/LoginModal";
import PasswordVisibleInvisible from "./Shared/PasswordVisibleInvisible";
import "react-datepicker/dist/react-datepicker.css";
import { successModalDto } from "@/Interface/Dto";
import SuccessfulModal from "./Modal/SuccessfulModal";
import { signIn } from "next-auth/react";
import ReCAPTCHA from "react-google-recaptcha";
import { showToast } from "@/utils";
import axios from "axios";

interface IQueryParams {
  email?: string;
  firstName?: string;
  lastName?: string;
}

const RegistrationForm = () => {
  const [showSuccessModal, setShowSuccessModal] = useState<successModalDto>({});
  const router = useRouter();
  const [openLoginModal, setOpenLoginModal] = useState<boolean>(false);
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<RegistrationInput>();

  const handleRecaptchaChange = (value) => setRecaptchaToken(value);
  const [passwordVisible, setPassWordVisible] = useState(false);
  const { email, firstName = '', lastName = '' } : IQueryParams = router.query;
  const [loading] = useState(false);


  useEffect(() => {
    if (email) {
      // If email is present in the url parameters
      setValue('email', email);
      setValue('firstname', firstName);
      setValue('lastname', lastName);

    }
  }, [email]);


  const onSubmit: SubmitHandler<any> = async (data: any) => {

    try {
        //User register
        const response: any = await postRegister({
          ...data,
          recaptchaToken,
        });

        if (response === false) {
          showToast(
            "This email already exist, please try another email",
            "warning"
          );
          return;
        }

      const { email: userEmail, password, firstname, lastname } = data;

      const WelcomeEmail = `
            
      <div>
      <div>
        <h1>Welcome to Cruise Collective's Newsletter!</h1>
        <p>
          Dear ${firstname + ' ' + lastname},<br />
          Welcome to Cruise Collective, your passport to a world of exclusive
          articles, enticing deals, and unbeatable discounts all centred
          around your next cruise holiday. We are thrilled to have you on
          board as a valued member of our growing community.
        </p>
        <p>
          Thank you for choosing to become a member, we extend our warmest
          welcome and hope to soon become your go-to source for all things
          cruise-related. Your membership unlocks a treasure trove of
          insights, insider information, and unparalleled opportunities that
          will make your next cruise experience even more extraordinary.
        </p>
        <p>
          At Cruise Collective, we've curated a collection of exclusive
          articles that delve into the latest trends, insider tips, and
          inspiring stories to fuel your wanderlust and enhance your cruise
          journeys. Be prepared to embark on a virtual voyage with us as we
          share the latest updates, behind-the-scenes glimpses, and expert
          advice.
        </p>

        <p>As a Cruise Collective member, you'll get:</p>

        <p>
          <strong>Exclusive Access:</strong> Gain priority access to
          unbeatable deals, significant discounts, and exciting competitions,
          making your cruise experiences truly extraordinary.
        </p>
        <p>
          <strong>Stay Informed:</strong>Stay up-to-date with the latest news
          directly from cruise lines, including live events and informative
          webinars, ensuring you're always in the know about upcoming voyages.
        </p>
        <p>
          <strong>Expert Reviews:</strong>Make informed decisions with our
          in-depth reviews of cruises and travel products. You'll cruise with
          confidence, knowing you've chosen the best.
        </p>

        <p>
          Cruise Collective is not just a magazine; it's your personal guide
          to unlocking the full potential of cruise holidays. We look forward
          to sharing the magic of cruising with you and making every voyage an
          unforgettable experience.
        </p>
        <p>Bon voyage!</p>
        <p>Cruise Collective Team</p>
      </div>
    </div>
      `;

      const body = {
        email: userEmail,
        subject: "Welcome Onboard from Cruise Collective",
        emailTemplate: WelcomeEmail,
      };
      await axios.post("/api/sendEmail", body);
       await signIn("credentials", {
        redirect: false,
        email: userEmail,
        password,
      });
      showToast("Logged in successfully.", "success");

      if (email) {
        router.push("/");
      } else {
        router.back();
      }
    } catch (error) {
      console.error(error);
    }
  };

  if (loading) {
    return <p className="mt-12">Loading...</p>;
  }

  return (
    <>
      <h2 className="text-base mb-4 mt-10 border-b opacity-20">
        Personal Information:
      </h2>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mt-10">
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2">
              First Name*
            </label>
            <input
              className="appearance-none border border-cruise rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              type="text"
              placeholder="First Name"
              {...register("firstname", { required: true })}
            />
            {errors.firstname && (
              <div className="text-red text-sm">Please enter a first name</div>
            )}
          </div>
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Last Name*
            </label>
            <input
              className="appearance-none border border-cruise rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              type="text"
              placeholder="Last Name"
              {...register("lastname", { required: true })}
            />
            {errors.lastname && (
              <div className="text-red text-sm">Please enter a last name</div>
            )}
          </div>

          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Email Address*
            </label>
            <input
              className="appearance-none border border-cruise rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              type="email"
              placeholder="Email Address"
              {...register("email", {
                required: "Email is required",
                pattern: {
                  value:
                    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                  message: "Please enter a valid email",
                },
              })}
            />
            {errors.email && (
              <div className="text-red text-sm">
                Please enter a valid email address
              </div>
            )}
          </div>

          {/* Password and password confirm fields */}
          <div className="relative">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Password*
            </label>
            <input
              className="appearance-none border border-cruise rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              type={`${passwordVisible ? "text" : "password"}`}
              placeholder="Password"
              {...register("password", {
                required: "Password is required",
                minLength: {
                  value: 6,
                  message: "Password must be at least 6 characters",
                },
              })}
            />

            <PasswordVisibleInvisible
              passwordVisible={passwordVisible}
              setPassWordVisible={setPassWordVisible}
            />

            {errors.password && (
              <div className="text-red text-sm">
                Please enter a valid password
              </div>
            )}
          </div>

          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Country*
            </label>
            <div className="relative">
              <select
                className="appearance-none border border-cruise rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                defaultValue=""
                {...register("country", { required: true })}
              >
                <option value="" disabled hidden>
                  Select Country
                </option>
                {countryList()
                  .getData()
                  .map((country) => (
                    <option key={country.value} value={country.value}>
                      {country.label}
                    </option>
                  ))}
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                <svg
                  className="fill-current h-4 w-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                >
                  <path d="M10 12l-8-8 1.5-1.5L10 9l6.5-6.5L18 4z" />
                </svg>
              </div>
            </div>
            {errors.country && (
              <div className="text-red text-sm">Please select a country</div>
            )}
          </div>
        </div>

        {/* GDPR Compliance */}
        <div className="mb-4 mt-10">
          <h2 className="text-base mb-2 border-b opacity-20">
            GDPR Compliance Fields:
          </h2>
          <label className="flex items-center mb-2 mt-8">
            <input
              type="checkbox"
              className="form-checkbox"
              {...register("terms", { required: true })}
            />

            <span className="ml-2 text-base">
              I agree to the{" "}
              <Link
                href="/terms-and-conditions"
                target="_blank"
                className="text-brand"
              >
                terms and conditions
              </Link>
            </span>
            {errors.terms && (
              <div className="text-red text-base ml-2">
                Please agree to the terms and conditions
              </div>
            )}
          </label>
          <label className="flex items-center mb-2">
            <input
              type="checkbox"
              className="form-checkbox"
              {...register("marketing", { required: true })}
            />
            <span className="ml-2 text-base">
              I agree to receive marketing emails from &apos;Cruise Collective&apos;
            </span>
            {errors.marketing && (
              <div className="text-red text-sm ml-2">
                Please agree to receive marketing emails
              </div>
            )}
          </label>
          <label className="flex items-center mb-2">
            <input
              type="checkbox"
              className="form-checkbox"
              {...register("privacy", { required: true })}
            />
            <span className="ml-2 text-base">
              I have read and understand the{" "}
              <Link
                href="/privacy-policy"
                target="_blank"
                className="text-brand"
              >
                privacy policy
              </Link>
            </span>
            {errors.privacy && (
              <div className="text-red text-base ml-2">
                Please agree to the privacy policy
              </div>
            )}
          </label>
        </div>
        <div className="mb-4">
          <ReCAPTCHA
            sitekey="6LeeeCkpAAAAAFYoAmYimoLjcMbODxltRez6FO0s"
            onChange={handleRecaptchaChange}
          />
        </div>
        {/* Register Button */}
        <div className="flex flex-col gap-4 items-center mb-2 mt-5">
          <button className="bg-orange w-[200px] h-[50px] text-white  text-sm apercu_regular uppercase tracking-[1.54px] hover:underline hover:text-black">
            Register
          </button>
          <div>
            Already have an account? &nbsp;
            <label
              className="text-brand apercu_regular_pro cursor-pointer"
              onClick={() => setOpenLoginModal(true)}
              htmlFor="login_modal_id"
            >
              Click here!
            </label>
          </div>
        </div>
      </form>

      {/* Login modal */}
      {openLoginModal && (
        <LoginModal
          openLoginModal={openLoginModal}
          setOpenLoginModal={setOpenLoginModal}
        />
      )}

      {/* Success modal */}
      {!!Object.keys(showSuccessModal).length && (
        <SuccessfulModal
          showSuccessModal={showSuccessModal}
          setShowSuccessModal={setShowSuccessModal}
        />
      )}
    </>
  );
};

export default RegistrationForm;
