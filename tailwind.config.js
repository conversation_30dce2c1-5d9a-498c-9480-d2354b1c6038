/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/atoms/**/*.{ts,tsx}',
    './src/components/**/*.{ts,tsx}',
    './src/containers/**/*.{ts,tsx}',
    './src/layout/**/*.{ts,tsx}',
    './src/pages/**/*.{ts,tsx}',
  ],
  theme: {
    colors: {
      transparent: 'transparent',
      current: 'currentColor',
      black: '#000',
      white: '#fff',
      cruise: '#F9B741',
      red: '#DC2626',
      brand: '#FF9A31',
      orange: '#DE8404',
      paper: '#F5F2EE',
      navy: '#233141',
      ash: 'rgba(0, 0, 0, 0.60)',
    },
    extend: {
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'collective-img-url': 'url("/images/slider-bg-collective.svg")',
        'card-hover': 'linear-gradient(rgba(11, 18, 55, 0.2), rgba(13, 16, 41, 0.8))',
        // 'card-hover': 'linear-gradient(0deg, rgba(35, 49, 65, 0.20) 0%, rgba(35, 49, 65, 0.20) 100%)',
        'card-background-opacity': 'linear-gradient(rgba(11, 18, 55, 0.1), rgba(13, 16, 41, 0.6))',
      },
      backgroundColor: {
        'black-transparent-rgba': 'rgba(35, 49, 65, 0.95)',
        'navy-rgba': 'rgba(35, 49, 65, 0.95)',
      },
      width: {
        55: '53%',
        45: '47%',
      },
      fontFamily: {
        poppins: ['var(--font-poppins)'],
        playfair_display: ['var(--font-playfair_display)'],
      },
    },
  },
  plugins: [],
};
