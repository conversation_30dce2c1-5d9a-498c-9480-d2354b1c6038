{"name": "cruise", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prettier:fix": "prettier './src/**/*.js' --write", "eslint:fix": "eslint ./src --fix", "codeStyle:fix": "npm run prettier:fix && npm run eslint:fix", "test": "jest --passWithNoTests --silent --detectOpenHandles --forceExit --coverage", "test:watch": "jest --watchAll --passWithNoTests --silent --detectOpenHandles --forceExit --coverage"}, "dependencies": {"@next/third-parties": "^14.1.0", "@reduxjs/toolkit": "^2.2.7", "@sendgrid/mail": "^7.7.0", "@types/node": "18.15.11", "@types/react": "18.0.33", "@types/react-dom": "18.0.11", "@types/react-tooltip": "^4.2.4", "@vimeo/player": "^2.24.0", "autoprefixer": "10.4.14", "axios": "^1.6.2", "caniuse-lite": "^1.0.30001660", "classnames": "^2.3.2", "clsx": "^1.2.1", "eslint": "^8.56.0", "eslint-config-next": "14.2.3", "localforage": "^1.10.0", "lucide": "^0.424.0", "next": "14.2.3", "next-auth": "^4.22.1", "next-redux-wrapper": "^8.1.0", "postcss": "8.4.21", "prettier": "2.8.7", "react": "18.2.0", "react-datepicker": "^4.21.0", "react-dom": "18.2.0", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.46.2", "react-html-parser": "^2.0.2", "react-infinite-scroll-component": "^6.1.0", "react-modal-video": "^2.0.1", "react-paginate": "^8.2.0", "react-query": "^3.39.3", "react-redux": "^9.1.2", "react-scroll-parallax": "^3.4.5", "react-select": "^5.7.7", "react-select-country-list": "^2.2.3", "react-toastify": "^9.1.3", "react-tooltip": "^5.22.0", "redux-persist": "^6.0.0", "sass": "^1.77.1", "sweetalert2": "^11.7.32", "swiper": "^9.2.2", "tailwindcss": "3.3.1", "typescript": "^5.3.3"}, "devDependencies": {"@svgr/webpack": "^7.0.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/jest": "^29.5.5", "@types/react-modal-video": "^1.2.3", "@types/react-paginate": "^7.1.1", "@typescript-eslint/eslint-plugin": "6.18.1", "@typescript-eslint/parser": "6.18.1", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react": "7.33.2", "eslint-plugin-unused-imports": "^3.0.0", "jest": "^29.7.0", "jest-cli": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "react-test-renderer": "18.2.0", "ts-node": "^10.9.2"}, "testEnvironment": "jest-environment-jsdom"}